import { Box, VStack, Link, Icon, useColorModeValue, Text, Tooltip, Divider, <PERSON>lapse, Button, useDisclosure, Badge } from '@chakra-ui/react';
import { FiServer, FiSettings, FiUsers, FiPackage, FiHelpCircle, FiMonitor, FiHome, FiDatabase, FiActivity, FiBox, FiCommand, FiChevronDown, FiAlertCircle } from 'react-icons/fi';
import { FaFileAlt, FaFlask } from 'react-icons/fa';
import NextLink from 'next/link';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import useGuildInfo from '../hooks/useGuildInfo';
import { useTheme } from '../contexts/ThemeContext';

// Import package.json version
const BOT_VERSION = '1.0.0'; // You can update this manually or import from package.json

const DEVELOPER_ID = '933023999770918932';

interface ApplicationItem {
  id: string;
  title: string;
  enabled: boolean;
  settings?: {
    openingSchedule?: {
      enabled: boolean;
      startDate: string;
      endDate: string;
    };
  };
}

export default function Sidebar() {
  const { data: session } = useSession();
  const router = useRouter();
  const isAdmin = (session?.user as any)?.isAdmin;
  const userId = (session?.user as any)?.id;
  const [isAdminExpanded, setIsAdminExpanded] = useState(false);
  const [isApplicationsExpanded, setIsApplicationsExpanded] = useState(false);
  const [applicationsOpen, setApplicationsOpen] = useState(false);
  const [openApplications, setOpenApplications] = useState<ApplicationItem[]>([]);
  const { displayName } = useGuildInfo();
  const { currentScheme } = useTheme();

  useEffect(() => {
    const fetchApplicationStatus = async () => {
      try {
        console.group('Sidebar Fetch Diagnostics');
        console.log('Starting application status fetch...');

        // Fetch application config
        const configRes = await fetch('/api/applications/config', {
          credentials: 'include'
        });

        if (!configRes.ok) {
          console.warn('Failed to fetch application config:', configRes.status);
          setApplicationsOpen(false);
          setOpenApplications([]);
          return;
        }

        const configData = await configRes.json();
        setApplicationsOpen(configData.isOpen || configData.open);

        // Fetch applications only if config indicates they're open
        if (configData.isOpen || configData.open) {
          try {
            const appsRes = await fetch('/api/admin/applications-builder', {
              credentials: 'include'
            });

            if (!appsRes.ok) {
              console.warn('Failed to fetch applications:', appsRes.status);
              setOpenApplications([]);
              return;
            }

            const appsData = await appsRes.json();
            setOpenApplications(appsData);
          } catch (appsError) {
            console.error('Error fetching applications:', appsError);
            setOpenApplications([]);
          }
        }

        // Attempt to fetch Discord channels with error handling
        try {
          const channelsRes = await fetch('/api/discord/channels', {
            credentials: 'include'
          });

          if (!channelsRes.ok) {
            console.warn('Failed to fetch Discord channels:', {
              status: channelsRes.status,
              statusText: channelsRes.statusText
            });
            
            // Try to get error details
            const errorDetails = await channelsRes.json().catch(() => ({}));
            console.warn('Channel fetch error details:', errorDetails);
          }
        } catch (channelsError) {
          console.error('Unexpected error fetching Discord channels:', channelsError);
        }

        console.groupEnd();
      } catch (error) {
        console.error('Comprehensive fetch error:', error);
        setApplicationsOpen(false);
        setOpenApplications([]);
      }
    };

    fetchApplicationStatus();
  }, []);

  const menuItems = [
    { name: 'Overview', icon: FiHome, href: '/overview' },
    { name: 'Applications', icon: FiPackage, href: '/applications' },
    { name: 'Tickets', icon: FiHelpCircle, href: '/tickets' },
    { name: 'Game Servers', icon: FiMonitor, href: '/gameservers' },
  ];

  // Admin functionality is now handled through the expandable admin section below

  const adminQuickLinks = [
    { name: 'Server Management', href: '/admin/guilds', icon: FiSettings },
    { name: 'Addons', href: '/admin/addons', icon: FiBox },
    { name: 'Errors', href: '/admin/errors', icon: FiAlertCircle },
  ];

  // All experimental features are now consolidated into the main Applications page

  // Experimental admin links for developers (Removed)

  const isActive = (href: string) => {
    if (href === '/overview') {
      return router.pathname === href;
    }
    return router.pathname.startsWith(href);
  };

  return (
    <Box
      as="nav"
      h="100%"
      bg={currentScheme.colors.surface}
      backdropFilter="blur(20px)"
      borderRight="1px solid"
      borderColor={currentScheme.colors.border}
      py={8}
      display="flex"
      flexDirection="column"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: `linear-gradient(180deg, ${currentScheme.colors.primary}15 0%, ${currentScheme.colors.accent}15 100%)`,
        zIndex: -1,
      }}
    >
      <VStack spacing={2} align="stretch" flex="1">
        {menuItems.map((item) => {
          const active = isActive(item.href);
          
          if (item.name === 'Applications') {
            return (
              <VStack spacing={0} align="stretch" key={item.name}>
                <Box
                  display="flex"
                  alignItems="center"
                  px={4}
                  py={3}
                  fontSize="sm"
                  fontWeight="medium"
                  color={isApplicationsExpanded || isActive(item.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                  bg={isActive(item.href) ? `${currentScheme.colors.primary}30` : 'transparent'}
                  _hover={{
                    bg: isActive(item.href) ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,
                    color: currentScheme.colors.text,
                    transform: 'translateX(4px)',
                  }}
                  _active={{
                    bg: `${currentScheme.colors.primary}50`,
                  }}
                  borderRight={isActive(item.href) ? '2px solid' : 'none'}
                  borderColor={isActive(item.href) ? currentScheme.colors.primary : 'transparent'}
                  transition="all 0.2s"
                  cursor="pointer"
                  role="group"
                  onClick={() => setIsApplicationsExpanded(!isApplicationsExpanded)}
                >
                  <Icon
                    as={item.icon}
                    w={5}
                    h={5}
                    mr={3}
                    transition="all 0.2s"
                    _groupHover={{
                      transform: 'scale(1.1)',
                    }}
                  />
                  <Text
                    display={{ base: 'none', lg: 'block' }}
                    bgGradient={isActive(item.href) ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none'}
                    bgClip={isActive(item.href) ? 'text' : 'none'}
                    transition="all 0.2s"
                    flex="1"
                  >
                    {item.name}
                  </Text>
                  <Icon
                    as={FiChevronDown}
                    w={4}
                    h={4}
                    ml={2}
                    transition="all 0.2s"
                    transform={isApplicationsExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
                    opacity={0.6}
                  />
                </Box>
                <Collapse in={isApplicationsExpanded} animateOpacity>
                  <VStack spacing={1} align="stretch" pl={4} py={2}>
                    {openApplications.length > 0 ? (
                      openApplications.map((app: ApplicationItem) => (
                        <Link
                          key={app.id}
                          as={NextLink}
                          href={`/applications/${app.id}`}
                          display="flex"
                          alignItems="center"
                          px={4}
                          py={2}
                          fontSize="xs"
                          fontWeight="medium"
                          color={isActive(`/applications/${app.id}`) ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                          bg={isActive(`/applications/${app.id}`) ? `${currentScheme.colors.primary}20` : 'transparent'}
                          _hover={{
                            bg: currentScheme.colors.surface,
                            color: currentScheme.colors.text,
                            transform: 'translateX(2px)',
                          }}
                          borderRadius="md"
                          transition="all 0.2s"
                          role="group"
                        >
                          <Text display={{ base: 'none', lg: 'block' }}>
                            {app.title}
                          </Text>
                        </Link>
                      ))
                    ) : (
                      <Text
                        px={4}
                        py={2}
                        fontSize="xs"
                        color={currentScheme.colors.textSecondary}
                        display={{ base: 'none', lg: 'block' }}
                      >
                        No open applications
                      </Text>
                    )}
                  </VStack>
                </Collapse>
              </VStack>
            );
          }
          return (
            <Tooltip
              key={item.name}
              label={item.name}
              placement="right"
              hasArrow
              gutter={20}
              openDelay={500}
              display={{ base: 'block', '2xl': 'none' }}
            >
              <Link
                as={NextLink}
                href={item.href}
                display="flex"
                alignItems="center"
                px={4}
                py={3}
                fontSize="sm"
                fontWeight="medium"
                color={active ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                bg={active ? `${currentScheme.colors.primary}30` : 'transparent'}
                _hover={{
                  bg: active ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,
                  color: currentScheme.colors.text,
                  transform: 'translateX(4px)',
                }}
                _active={{
                  bg: `${currentScheme.colors.primary}50`,
                }}
                borderRight={active ? '2px solid' : 'none'}
                borderColor={active ? currentScheme.colors.primary : 'transparent'}
                transition="all 0.2s"
                role="group"
              >
                <Icon
                  as={item.icon}
                  w={5}
                  h={5}
                  mr={3}
                  transition="all 0.2s"
                  _groupHover={{
                    transform: 'scale(1.1)',
                  }}
                />
                <Text
                  display={{ base: 'none', lg: 'block' }}
                  bgGradient={active ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none'}
                  bgClip={active ? 'text' : 'none'}
                  transition="all 0.2s"
                >
                  {item.name}
                </Text>
                {item.name === 'Applications' && !applicationsOpen && (
                  <Badge
                    ml={2}
                    colorScheme="red"
                    variant="solid"
                    fontSize="xs"
                    display={{ base: 'none', lg: 'block' }}
                  >
                    Closed
                  </Badge>
                )}
                {item.name === 'Applications' && openApplications.length > 0 && (
                  <Badge
                    ml={2}
                    colorScheme="green"
                    variant="solid"
                    fontSize="xs"
                    display={{ base: 'none', lg: 'block' }}
                  >
                    {openApplications.length} Open
                  </Badge>
                )}
              </Link>
            </Tooltip>
          );
        })}
        
        {/* Admin Expandable Section */}
        {isAdmin && (
          <VStack spacing={0} align="stretch">
            <Box
              display="flex"
              alignItems="center"
              px={4}
              py={3}
              fontSize="sm"
              fontWeight="medium"
              color={currentScheme.colors.textSecondary}
              bg="transparent"
              _hover={{
                bg: currentScheme.colors.surface,
                color: currentScheme.colors.text,
                transform: 'translateX(4px)',
              }}
              transition="all 0.2s"
              cursor="pointer"
              role="group"
              onClick={() => setIsAdminExpanded(!isAdminExpanded)}
            >
              <Icon
                as={FiServer}
                w={5}
                h={5}
                mr={3}
                transition="all 0.2s"
                _groupHover={{
                  transform: 'scale(1.1)',
                }}
              />
              <Text
                display={{ base: 'none', lg: 'block' }}
                transition="all 0.2s"
                flex="1"
              >
                Admin
              </Text>
              <Icon
                as={FiChevronDown}
                w={4}
                h={4}
                ml={2}
                transition="all 0.2s"
                transform={isAdminExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
                opacity={0.6}
              />
            </Box>
            
            <Collapse in={isAdminExpanded} animateOpacity>
              <VStack spacing={1} align="stretch" pl={4} py={2}>
                {adminQuickLinks.map((link) => (
                  <Link
                    key={link.href}
                    as={NextLink}
                    href={link.href}
                    display="flex"
                    alignItems="center"
                    px={4}
                    py={2}
                    fontSize="xs"
                    fontWeight="medium"
                    color={isActive(link.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                    bg={isActive(link.href) ? `${currentScheme.colors.primary}20` : 'transparent'}
                    _hover={{
                      bg: currentScheme.colors.surface,
                      color: currentScheme.colors.text,
                      transform: 'translateX(2px)',
                    }}
                    borderRadius="md"
                    transition="all 0.2s"
                    role="group"
                  >
                    <Icon
                      as={link.icon}
                      w={4}
                      h={4}
                      mr={2}
                      transition="all 0.2s"
                      _groupHover={{
                        transform: 'scale(1.1)',
                      }}
                    />
                    <Text display={{ base: 'none', lg: 'block' }}>
                      {link.name}
                    </Text>
                  </Link>
                ))}
              </VStack>
            </Collapse>
          </VStack>
        )}
      </VStack>

      {/* Version display at bottom */}
      <Box px={4} pt={4} mt="auto">
        <Divider borderColor={currentScheme.colors.border} mb={4} />
        <Text
          fontSize="xs"
          color={currentScheme.colors.textSecondary}
          textAlign="center"
          bgGradient={`linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})`}
          bgClip="text"
          opacity={0.7}
          _hover={{
            opacity: 1,
            transform: "scale(1.05)"
          }}
          transition="all 0.2s"
        >
          {displayName ? `${displayName} v${BOT_VERSION}` : `Bot v${BOT_VERSION}`}
        </Text>
      </Box>
    </Box>
  );
} 
{"name": "404-bot", "version": "1.0.0", "description": "A Discord bot with TypeScript, addon system, MongoDB integration and web dashboard", "type": "module", "main": "dist/index.js", "scripts": {"dev:bot": "tsx watch src/index.ts", "dev": "npm run dev:full", "dev:dashboard": "cd src/dashboard && next dev -H 0.0.0.0 -p 3000", "dev:full": "concurrently -k \"npm run dev:bot\" \"npm run dev:dashboard\"", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "node scripts/build-optimized.js", "build:legacy": "tsc --project tsconfig.json && npm run copy-files && npm run build:dashboard", "build:dashboard": "rimraf src/dashboard/.next && cd src/dashboard && next build", "clean:dashboard": "rimraf src/dashboard/.next", "start:dashboard": "cd src/dashboard && next start -H 0.0.0.0 -p 3000", "copy-files": "powershell -Command \"Get-ChildItem -Path src -Recurse -Include config.yml,*.html,*.css,*.png,*.jpg,*.svg | ForEach-Object { $destPath = $_.FullName.Replace('\\src\\', '\\dist\\'); New-Item -ItemType Directory -Force -Path (Split-Path $destPath); Copy-Item $_.FullName -Destination $destPath -Force }\"", "start:bot": "node --enable-source-maps dist/index.js", "start:full": "concurrently -k \"npm run start:bot\" \"npm run start:dashboard\"", "start": "npm run start:bot", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint . --cache --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --fix --cache --ext .js,.jsx,.ts,.tsx", "format": "prettier --check .", "format:fix": "prettier --write .", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "commands:register": "npm run build && node --enable-source-maps dist/index.js commands register", "commands:delete": "npm run build && node --enable-source-maps dist/index.js commands delete", "commands:clear": "npm run build && node --enable-source-maps dist/index.js commands clear", "discord:diagnose": "tsx scripts/discord-bot-diagnostics.ts"}, "keywords": ["discord", "bot", "typescript", "mongodb", "addons", "dashboard"], "author": "Your Name", "license": "MIT", "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@discordjs/rest": "^2.5.1", "@discordjs/voice": "^0.18.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@types/winston": "^2.4.4", "ace-builds": "^1.35.2", "bufferutil": "^4.0.9", "chalk": "^5.4.1", "chart.js": "^4.5.0", "chokidar": "^4.0.1", "date-fns": "^4.1.0", "discord-api-types": "^0.38.14", "fast-glob": "^3.3.2", "framer-motion": "^12.23.1", "gamedig": "^5.3.1", "mongodb": "^6.17.0", "next": "^15.3.5", "next-auth": "^4.24.11", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "react": "19.1.0", "react-ace": "^11.0.1", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.0.1", "reactflow": "^11.11.4", "recharts": "^3.1.0", "swr": "^2.2.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zlib-sync": "^0.1.10"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitest/coverage-v8": "^1.2.2", "autoprefixer": "^10.4.17", "concurrently": "^8.2.0", "discord.js": "^14.21.0", "eslint": "^9.29.0", "postcss": "^8.4.33", "prettier": "^3.2.5", "rimraf": "^6.0.1", "sharp": "^0.34.2", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^1.2.2", "yaml": "^2.6.1"}, "engines": {"node": ">=18.0.0"}, "pnpm": {"overrides": {"glob@<8.0.0": "^10.3.10", "inflight@<2.0.0": "^2.0.1"}}}
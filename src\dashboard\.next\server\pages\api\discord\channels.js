"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/channels";
exports.ids = ["pages/api/discord/channels"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\channels.ts */ \"(api-node)/./pages/api/discord/channels.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/channels\",\n        pathname: \"/api/discord/channels\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2NvcmUvY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEsY0FBYztBQUNXO0FBQ0Q7QUFDSztBQUU3QixJQUFJRyxTQUFjLENBQUM7QUFFbkIsSUFBSTtJQUNGLGtGQUFrRjtJQUNsRixNQUFNQyxXQUFXO1FBQ2Y7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNELENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0oseUNBQVksQ0FBQ00sUUFBUUMsR0FBRyxJQUFJSDtJQUV6QyxJQUFJSSxhQUFhTixTQUFTTyxJQUFJLENBQUNDLENBQUFBLElBQUtaLDBDQUFhLENBQUNZO0lBRWxELElBQUksQ0FBQ0YsWUFBWTtRQUNmLHFDQUFxQztRQUNyQyxNQUFNSSxXQUFXWix5Q0FBWSxDQUFDYSxXQUFXO1FBQ3pDLElBQUlmLDBDQUFhLENBQUNjLFdBQVdKLGFBQWFJO0lBQzVDO0lBRUEsSUFBSSxDQUFDSixZQUFZO1FBQ2YsTUFBTSxJQUFJTSxNQUFNO0lBQ2xCO0lBRUEsTUFBTUMsZUFBZWpCLDRDQUFlLENBQUNVLFlBQVk7SUFDakRQLFNBQVNGLGlEQUFVLENBQUNnQjtBQUN0QixFQUFFLE9BQU9HLE9BQU87SUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7SUFDbkRaLFFBQVFjLElBQUksQ0FBQyxJQUFJLG1DQUFtQztBQUN0RDtBQUVBLHVDQUF1QztBQUNoQyxNQUFNQyxrQkFBa0I7SUFDN0JDLEtBQUs7UUFDSEMsT0FBT3RCLE9BQU9xQixHQUFHLENBQUNDLEtBQUs7UUFDdkJDLFVBQVV2QixPQUFPcUIsR0FBRyxDQUFDRSxRQUFRO1FBQzdCQyxjQUFjeEIsT0FBT3FCLEdBQUcsQ0FBQ0csWUFBWTtRQUNyQ0MsU0FBU3pCLE9BQU9xQixHQUFHLENBQUNJLE9BQU87UUFDM0JDLGtCQUFrQjFCLE9BQU9xQixHQUFHLENBQUNLLGdCQUFnQixJQUFJO1FBQ2pEQyxvQkFBb0IzQixPQUFPcUIsR0FBRyxDQUFDTSxrQkFBa0IsSUFBSTtRQUNyREMsUUFBUTVCLE9BQU9xQixHQUFHLENBQUNPLE1BQU07SUFDM0I7SUFDQUMsV0FBVztRQUNUQyxRQUFROUIsT0FBTzZCLFNBQVMsRUFBRUMsVUFBVSxFQUFFO1FBQ3RDQyxjQUFjL0IsT0FBTzZCLFNBQVMsRUFBRUUsZ0JBQWdCLEVBQUU7UUFDbERDLFNBQVM7WUFDUEMsUUFBUWpDLE9BQU82QixTQUFTLEVBQUVHLFNBQVNDLFVBQVVqQyxPQUFPcUIsR0FBRyxDQUFDRyxZQUFZO1FBQ3RFO0lBQ0Y7SUFDQVUsVUFBVTtRQUNSQyxLQUFLbkMsT0FBT2tDLFFBQVEsQ0FBQ0MsR0FBRztRQUN4QkMsTUFBTXBDLE9BQU9rQyxRQUFRLENBQUNFLElBQUk7UUFDMUJDLFNBQVM7WUFDUEMsYUFBYXRDLE9BQU9rQyxRQUFRLENBQUNHLE9BQU8sRUFBRUMsZUFBZTtZQUNyREMsYUFBYXZDLE9BQU9rQyxRQUFRLENBQUNHLE9BQU8sRUFBRUUsZUFBZTtZQUNyREMsZUFBZXhDLE9BQU9rQyxRQUFRLENBQUNHLE9BQU8sRUFBRUcsaUJBQWlCO1lBQ3pEQywwQkFBMEJ6QyxPQUFPa0MsUUFBUSxDQUFDRyxPQUFPLEVBQUVJLDRCQUE0QjtZQUMvRUMsaUJBQWlCMUMsT0FBT2tDLFFBQVEsQ0FBQ0csT0FBTyxFQUFFSyxtQkFBbUI7WUFDN0RDLGtCQUFrQjNDLE9BQU9rQyxRQUFRLENBQUNHLE9BQU8sRUFBRU0sb0JBQW9CO1lBQy9EQyxhQUFhNUMsT0FBT2tDLFFBQVEsQ0FBQ0csT0FBTyxFQUFFTyxnQkFBZ0I7WUFDdERDLFlBQVk3QyxPQUFPa0MsUUFBUSxDQUFDRyxPQUFPLEVBQUVRLGVBQWU7UUFDdEQ7SUFDRjtBQUNGLEVBQUU7QUFFRixrQ0FBa0M7QUFDbEMsSUFBSSxDQUFDekIsZ0JBQWdCQyxHQUFHLENBQUNDLEtBQUssRUFBRTtJQUM5QkosUUFBUUQsS0FBSyxDQUFDO0lBQ2RaLFFBQVFjLElBQUksQ0FBQztBQUNmO0FBRUEsSUFBSSxDQUFDQyxnQkFBZ0JDLEdBQUcsQ0FBQ0UsUUFBUSxJQUFJLENBQUNILGdCQUFnQkMsR0FBRyxDQUFDRyxZQUFZLEVBQUU7SUFDdEVOLFFBQVFELEtBQUssQ0FBQztJQUNkWixRQUFRYyxJQUFJLENBQUM7QUFDZjtBQUVBLElBQUksQ0FBQ0MsZ0JBQWdCQyxHQUFHLENBQUNJLE9BQU8sRUFBRTtJQUNoQ1AsUUFBUUQsS0FBSyxDQUFDO0lBQ2RaLFFBQVFjLElBQUksQ0FBQztBQUNmO0FBRUEsSUFBSSxDQUFDQyxnQkFBZ0JjLFFBQVEsQ0FBQ0MsR0FBRyxJQUFJLENBQUNmLGdCQUFnQmMsUUFBUSxDQUFDRSxJQUFJLEVBQUU7SUFDbkVsQixRQUFRRCxLQUFLLENBQUM7SUFDZFosUUFBUWMsSUFBSSxDQUFDO0FBQ2Y7QUFFQSxpRUFBZUMsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcc3JjXFxkYXNoYm9hcmRcXGNvcmVcXGNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAdHMtbm9jaGVja1xyXG5pbXBvcnQgKiBhcyBmcyBmcm9tICdmcyc7XHJcbmltcG9ydCBZQU1MIGZyb20gJ3lhbWwnO1xyXG5pbXBvcnQgKiBhcyBwYXRoIGZyb20gJ3BhdGgnO1xyXG5cclxubGV0IGNvbmZpZzogYW55ID0ge307XHJcblxyXG50cnkge1xyXG4gIC8vIExvY2F0ZSBjb25maWcueW1sIGJ5IHdhbGtpbmcgdXAgZGlyZWN0b3JpZXMgKGRhc2hib2FyZCBtYXkgcnVuIGZyb20gbmVzdGVkIGN3ZClcclxuICBjb25zdCBwb3NzaWJsZSA9IFtcclxuICAgICdjb25maWcueW1sJyxcclxuICAgICcuLi9jb25maWcueW1sJyxcclxuICAgICcuLi8uLi9jb25maWcueW1sJyxcclxuICAgICcuLi8uLi8uLi9jb25maWcueW1sJyxcclxuICAgICcuLi8uLi8uLi8uLi9jb25maWcueW1sJyxcclxuICBdLm1hcChyZWwgPT4gcGF0aC5yZXNvbHZlKHByb2Nlc3MuY3dkKCksIHJlbCkpO1xyXG5cclxuICBsZXQgY29uZmlnUGF0aCA9IHBvc3NpYmxlLmZpbmQocCA9PiBmcy5leGlzdHNTeW5jKHApKTtcclxuXHJcbiAgaWYgKCFjb25maWdQYXRoKSB7XHJcbiAgICAvLyBmYWxsYmFjayByZWxhdGl2ZSB0byBmaWxlIGxvY2F0aW9uXHJcbiAgICBjb25zdCBkaXJCYXNlZCA9IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUsICcuLi8uLi8uLi9jb25maWcueW1sJyk7XHJcbiAgICBpZiAoZnMuZXhpc3RzU3luYyhkaXJCYXNlZCkpIGNvbmZpZ1BhdGggPSBkaXJCYXNlZDtcclxuICB9XHJcblxyXG4gIGlmICghY29uZmlnUGF0aCkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCdjb25maWcueW1sIG5vdCBmb3VuZCcpO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgZmlsZUNvbnRlbnRzID0gZnMucmVhZEZpbGVTeW5jKGNvbmZpZ1BhdGgsICd1dGY4Jyk7XHJcbiAgY29uZmlnID0gWUFNTC5wYXJzZShmaWxlQ29udGVudHMpO1xyXG59IGNhdGNoIChlcnJvcikge1xyXG4gIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOiBGYWlsZWQgdG8gbG9hZCBjb25maWcueW1sOicsIGVycm9yKTtcclxuICBwcm9jZXNzLmV4aXQoMSk7IC8vIEV4aXQgaWYgd2UgY2FuJ3QgbG9hZCB0aGUgY29uZmlnXHJcbn1cclxuXHJcbi8vIEV4cG9ydCB0aGUgZGFzaGJvYXJkLXNwZWNpZmljIGNvbmZpZ1xyXG5leHBvcnQgY29uc3QgZGFzaGJvYXJkQ29uZmlnID0ge1xyXG4gIGJvdDoge1xyXG4gICAgdG9rZW46IGNvbmZpZy5ib3QudG9rZW4sXHJcbiAgICBjbGllbnRJZDogY29uZmlnLmJvdC5jbGllbnRJZCxcclxuICAgIGNsaWVudFNlY3JldDogY29uZmlnLmJvdC5jbGllbnRTZWNyZXQsXHJcbiAgICBndWlsZElkOiBjb25maWcuYm90Lmd1aWxkSWQsXHJcbiAgICB0aWNrZXRDYXRlZ29yeUlkOiBjb25maWcuYm90LnRpY2tldENhdGVnb3J5SWQgfHwgbnVsbCxcclxuICAgIHRpY2tldExvZ0NoYW5uZWxJZDogY29uZmlnLmJvdC50aWNrZXRMb2dDaGFubmVsSWQgfHwgbnVsbCxcclxuICAgIHByZWZpeDogY29uZmlnLmJvdC5wcmVmaXgsXHJcbiAgfSxcclxuICBkYXNoYm9hcmQ6IHtcclxuICAgIGFkbWluczogY29uZmlnLmRhc2hib2FyZD8uYWRtaW5zIHx8IFtdLFxyXG4gICAgYWRtaW5Sb2xlSWRzOiBjb25maWcuZGFzaGJvYXJkPy5hZG1pblJvbGVJZHMgfHwgW10sXHJcbiAgICBzZXNzaW9uOiB7XHJcbiAgICAgIHNlY3JldDogY29uZmlnLmRhc2hib2FyZD8uc2Vzc2lvbj8uc2VjcmV0IHx8IGNvbmZpZy5ib3QuY2xpZW50U2VjcmV0LCAvLyBGYWxsYmFjayB0byBjbGllbnRTZWNyZXQgaWYgbm8gc2Vzc2lvbiBzZWNyZXRcclxuICAgIH0sXHJcbiAgfSxcclxuICBkYXRhYmFzZToge1xyXG4gICAgdXJsOiBjb25maWcuZGF0YWJhc2UudXJsLFxyXG4gICAgbmFtZTogY29uZmlnLmRhdGFiYXNlLm5hbWUsXHJcbiAgICBvcHRpb25zOiB7XHJcbiAgICAgIG1heFBvb2xTaXplOiBjb25maWcuZGF0YWJhc2Uub3B0aW9ucz8ubWF4UG9vbFNpemUgfHwgMTAsXHJcbiAgICAgIG1pblBvb2xTaXplOiBjb25maWcuZGF0YWJhc2Uub3B0aW9ucz8ubWluUG9vbFNpemUgfHwgMSxcclxuICAgICAgbWF4SWRsZVRpbWVNUzogY29uZmlnLmRhdGFiYXNlLm9wdGlvbnM/Lm1heElkbGVUaW1lTVMgfHwgMzAwMDAsXHJcbiAgICAgIHNlcnZlclNlbGVjdGlvblRpbWVvdXRNUzogY29uZmlnLmRhdGFiYXNlLm9wdGlvbnM/LnNlcnZlclNlbGVjdGlvblRpbWVvdXRNUyB8fCA1MDAwLFxyXG4gICAgICBzb2NrZXRUaW1lb3V0TVM6IGNvbmZpZy5kYXRhYmFzZS5vcHRpb25zPy5zb2NrZXRUaW1lb3V0TVMgfHwgNDUwMDAsXHJcbiAgICAgIGNvbm5lY3RUaW1lb3V0TVM6IGNvbmZpZy5kYXRhYmFzZS5vcHRpb25zPy5jb25uZWN0VGltZW91dE1TIHx8IDEwMDAwLFxyXG4gICAgICByZXRyeVdyaXRlczogY29uZmlnLmRhdGFiYXNlLm9wdGlvbnM/LnJldHJ5V3JpdGVzICE9PSBmYWxzZSxcclxuICAgICAgcmV0cnlSZWFkczogY29uZmlnLmRhdGFiYXNlLm9wdGlvbnM/LnJldHJ5UmVhZHMgIT09IGZhbHNlLFxyXG4gICAgfSxcclxuICB9LFxyXG59O1xyXG5cclxuLy8gVmFsaWRhdGUgcmVxdWlyZWQgY29uZmlndXJhdGlvblxyXG5pZiAoIWRhc2hib2FyZENvbmZpZy5ib3QudG9rZW4pIHtcclxuICBjb25zb2xlLmVycm9yKCdFcnJvcjogRGlzY29yZCBib3QgdG9rZW4gaXMgcmVxdWlyZWQgaW4gY29uZmlnLnltbCcpO1xyXG4gIHByb2Nlc3MuZXhpdCgxKTtcclxufVxyXG5cclxuaWYgKCFkYXNoYm9hcmRDb25maWcuYm90LmNsaWVudElkIHx8ICFkYXNoYm9hcmRDb25maWcuYm90LmNsaWVudFNlY3JldCkge1xyXG4gIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOiBEaXNjb3JkIE9BdXRoMiBjcmVkZW50aWFscyAoY2xpZW50SWQgYW5kIGNsaWVudFNlY3JldCkgYXJlIHJlcXVpcmVkIGluIGNvbmZpZy55bWwnKTtcclxuICBwcm9jZXNzLmV4aXQoMSk7XHJcbn1cclxuXHJcbmlmICghZGFzaGJvYXJkQ29uZmlnLmJvdC5ndWlsZElkKSB7XHJcbiAgY29uc29sZS5lcnJvcignRXJyb3I6IEd1aWxkIElEIGlzIHJlcXVpcmVkIGluIGNvbmZpZy55bWwnKTtcclxuICBwcm9jZXNzLmV4aXQoMSk7XHJcbn1cclxuXHJcbmlmICghZGFzaGJvYXJkQ29uZmlnLmRhdGFiYXNlLnVybCB8fCAhZGFzaGJvYXJkQ29uZmlnLmRhdGFiYXNlLm5hbWUpIHtcclxuICBjb25zb2xlLmVycm9yKCdFcnJvcjogRGF0YWJhc2UgY29uZmlndXJhdGlvbiAodXJsIGFuZCBuYW1lKSBpcyByZXF1aXJlZCBpbiBjb25maWcueW1sJyk7XHJcbiAgcHJvY2Vzcy5leGl0KDEpO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBkYXNoYm9hcmRDb25maWc7ICJdLCJuYW1lcyI6WyJmcyIsIllBTUwiLCJwYXRoIiwiY29uZmlnIiwicG9zc2libGUiLCJtYXAiLCJyZWwiLCJyZXNvbHZlIiwicHJvY2VzcyIsImN3ZCIsImNvbmZpZ1BhdGgiLCJmaW5kIiwicCIsImV4aXN0c1N5bmMiLCJkaXJCYXNlZCIsIl9fZGlybmFtZSIsIkVycm9yIiwiZmlsZUNvbnRlbnRzIiwicmVhZEZpbGVTeW5jIiwicGFyc2UiLCJlcnJvciIsImNvbnNvbGUiLCJleGl0IiwiZGFzaGJvYXJkQ29uZmlnIiwiYm90IiwidG9rZW4iLCJjbGllbnRJZCIsImNsaWVudFNlY3JldCIsImd1aWxkSWQiLCJ0aWNrZXRDYXRlZ29yeUlkIiwidGlja2V0TG9nQ2hhbm5lbElkIiwicHJlZml4IiwiZGFzaGJvYXJkIiwiYWRtaW5zIiwiYWRtaW5Sb2xlSWRzIiwic2Vzc2lvbiIsInNlY3JldCIsImRhdGFiYXNlIiwidXJsIiwibmFtZSIsIm9wdGlvbnMiLCJtYXhQb29sU2l6ZSIsIm1pblBvb2xTaXplIiwibWF4SWRsZVRpbWVNUyIsInNlcnZlclNlbGVjdGlvblRpbWVvdXRNUyIsInNvY2tldFRpbWVvdXRNUyIsImNvbm5lY3RUaW1lb3V0TVMiLCJyZXRyeVdyaXRlcyIsInJldHJ5UmVhZHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/./core/config.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/channels.ts":
/*!***************************************!*\
  !*** ./pages/api/discord/channels.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\n// Discord channel type mapping\nconst CHANNEL_TYPES = {\n    GUILD_TEXT: 0,\n    GUILD_VOICE: 2,\n    GUILD_CATEGORY: 4,\n    GUILD_ANNOUNCEMENT: 5,\n    GUILD_ANNOUNCEMENT_THREAD: 10,\n    GUILD_PUBLIC_THREAD: 11,\n    GUILD_PRIVATE_THREAD: 12,\n    GUILD_STAGE_VOICE: 13,\n    GUILD_FORUM: 15\n};\n// Reverse mapping for type conversion\nconst CHANNEL_TYPE_NAMES = {\n    0: 'GUILD_TEXT',\n    2: 'GUILD_VOICE',\n    4: 'GUILD_CATEGORY',\n    5: 'GUILD_ANNOUNCEMENT',\n    10: 'GUILD_ANNOUNCEMENT_THREAD',\n    11: 'GUILD_PUBLIC_THREAD',\n    12: 'GUILD_PRIVATE_THREAD',\n    13: 'GUILD_STAGE_VOICE',\n    15: 'GUILD_FORUM'\n};\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // For sensitive operations like creating channels we still require admin.\n        const isAdmin = session.user.isAdmin;\n        const { guildId, token } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot;\n        if (!token || !guildId) {\n            console.error('Missing bot configuration');\n            return res.status(500).json({\n                error: 'Bot configuration missing'\n            });\n        }\n        if (req.method === 'GET') {\n            try {\n                // Validate bot configuration\n                if (!token) {\n                    console.error('No bot token provided');\n                    return res.status(500).json({\n                        error: 'Bot configuration incomplete',\n                        details: 'No bot token found'\n                    });\n                }\n                if (!guildId) {\n                    console.error('No guild ID provided');\n                    return res.status(500).json({\n                        error: 'Bot configuration incomplete',\n                        details: 'No guild ID found'\n                    });\n                }\n                // Log detailed request information\n                console.log('Channels API Request Details:', {\n                    method: req.method,\n                    query: req.query,\n                    headers: req.headers,\n                    guildId,\n                    tokenProvided: !!token\n                });\n                // Attempt to fetch channels with detailed error handling\n                const fetchOptions = {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    }\n                };\n                console.log('Fetching Discord channels with options:', {\n                    url: `https://discord.com/api/v10/guilds/${guildId}/channels`,\n                    headers: {\n                        Authorization: 'Bot [REDACTED]',\n                        'Content-Type': 'application/json'\n                    }\n                });\n                // Validate bot permissions before making the request\n                const permissionsCheckResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}`, {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (!permissionsCheckResponse.ok) {\n                    const permissionsErrorBody = await permissionsCheckResponse.text();\n                    console.error('Bot Permissions Check Failed:', {\n                        status: permissionsCheckResponse.status,\n                        statusText: permissionsCheckResponse.statusText,\n                        body: permissionsErrorBody\n                    });\n                    return res.status(permissionsCheckResponse.status).json({\n                        error: 'Bot permissions check failed',\n                        status: permissionsCheckResponse.status,\n                        details: permissionsErrorBody\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, fetchOptions);\n                // Log raw response details\n                console.log('Discord API Response:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: Object.fromEntries(response.headers.entries())\n                });\n                if (!response.ok) {\n                    // Attempt to parse error response\n                    let errorBody;\n                    try {\n                        errorBody = await response.text();\n                    } catch (parseError) {\n                        errorBody = 'Unable to parse error response';\n                    }\n                    console.error('Discord API Error:', {\n                        status: response.status,\n                        statusText: response.statusText,\n                        body: errorBody\n                    });\n                    return res.status(response.status).json({\n                        error: 'Failed to fetch channels from Discord',\n                        status: response.status,\n                        details: errorBody\n                    });\n                }\n                const channels = await response.json();\n                // Log number of channels fetched\n                console.log(`Successfully fetched ${channels.length} channels`);\n                // Format channels based on the request source\n                const formattedChannels = channels.map((channel)=>{\n                    // Base channel format\n                    const formattedChannel = {\n                        id: channel.id,\n                        name: channel.name,\n                        type: CHANNEL_TYPE_NAMES[channel.type] || 'unknown',\n                        position: channel.position\n                    };\n                    // Add parent_id if it exists\n                    if (channel.parent_id) {\n                        formattedChannel.parent_id = channel.parent_id;\n                    }\n                    // Add raw type for the channel management page\n                    formattedChannel.raw_type = channel.type;\n                    // Add additional properties based on channel type\n                    if (channel.type === CHANNEL_TYPES.GUILD_TEXT) {\n                        formattedChannel.topic = channel.topic;\n                        formattedChannel.nsfw = channel.nsfw;\n                        formattedChannel.rate_limit_per_user = channel.rate_limit_per_user;\n                    } else if (channel.type === CHANNEL_TYPES.GUILD_VOICE) {\n                        formattedChannel.bitrate = channel.bitrate;\n                        formattedChannel.user_limit = channel.user_limit;\n                    }\n                    return formattedChannel;\n                });\n                return res.status(200).json(formattedChannels);\n            } catch (error) {\n                // Comprehensive error logging\n                console.error('Comprehensive error fetching channels:', {\n                    message: error.message,\n                    stack: error.stack,\n                    guildId,\n                    tokenProvided: !!token\n                });\n                return res.status(500).json({\n                    error: 'Unexpected error fetching channels',\n                    details: error.message,\n                    guildId: guildId ? 'Provided' : 'Missing',\n                    tokenProvided: !!token\n                });\n            }\n        }\n        if (req.method === 'POST') {\n            // Creating channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const { name, type, topic, nsfw, bitrate, userLimit, parent, position, rateLimitPerUser } = req.body;\n                console.log('Received channel creation request:', req.body);\n                // Validate required fields\n                if (!name || typeof type !== 'number' && typeof type !== 'string') {\n                    console.log('Validation failed:', {\n                        name,\n                        type\n                    });\n                    return res.status(400).json({\n                        error: 'Name and type are required'\n                    });\n                }\n                // Convert string type to numeric if needed\n                let numericType = typeof type === 'number' ? type : CHANNEL_TYPES[type];\n                console.log('Channel type conversion:', {\n                    original: type,\n                    converted: numericType\n                });\n                if (typeof numericType !== 'number') {\n                    return res.status(400).json({\n                        error: 'Invalid channel type'\n                    });\n                }\n                // Prepare channel data based on type\n                const channelData = {\n                    name,\n                    type: numericType,\n                    position: position || 0\n                };\n                // Add type-specific properties\n                if (numericType === CHANNEL_TYPES.GUILD_TEXT) {\n                    if (topic) channelData.topic = topic;\n                    channelData.nsfw = Boolean(nsfw);\n                    if (typeof rateLimitPerUser === 'number' && !isNaN(rateLimitPerUser)) {\n                        channelData.rate_limit_per_user = rateLimitPerUser;\n                    }\n                } else if (numericType === CHANNEL_TYPES.GUILD_VOICE) {\n                    if (typeof bitrate === 'number' && !isNaN(bitrate)) {\n                        channelData.bitrate = bitrate;\n                    }\n                    if (typeof userLimit === 'number' && !isNaN(userLimit)) {\n                        channelData.user_limit = userLimit;\n                    }\n                }\n                // Add parent category if specified\n                if (parent && numericType !== CHANNEL_TYPES.GUILD_CATEGORY) {\n                    channelData.parent_id = parent;\n                }\n                console.log('Creating channel with data:', channelData);\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {\n                    method: 'POST',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(channelData)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const newChannel = await response.json();\n                return res.status(201).json(newChannel);\n            } catch (error) {\n                console.error('Error creating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to create channel'\n                });\n            }\n        }\n        if (req.method === 'PATCH') {\n            // Editing channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'PATCH',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(req.body)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const updatedChannel = await response.json();\n                return res.status(200).json(updatedChannel);\n            } catch (error) {\n                console.error('Error updating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to update channel'\n                });\n            }\n        }\n        if (req.method === 'DELETE') {\n            // Deleting channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                return res.status(200).json({\n                    message: 'Channel deleted successfully'\n                });\n            } catch (error) {\n                console.error('Error deleting channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to delete channel'\n                });\n            }\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in channel handler:', error);\n        return res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL3BhZ2VzL2FwaS9kaXNjb3JkL2NoYW5uZWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsY0FBYztBQUVvQztBQUNFO0FBQ0c7QUFFdkQsK0JBQStCO0FBQy9CLE1BQU1HLGdCQUFnQjtJQUNwQkMsWUFBWTtJQUNaQyxhQUFhO0lBQ2JDLGdCQUFnQjtJQUNoQkMsb0JBQW9CO0lBQ3BCQywyQkFBMkI7SUFDM0JDLHFCQUFxQjtJQUNyQkMsc0JBQXNCO0lBQ3RCQyxtQkFBbUI7SUFDbkJDLGFBQWE7QUFDZjtBQUVBLHNDQUFzQztBQUN0QyxNQUFNQyxxQkFBcUI7SUFDekIsR0FBRztJQUNILEdBQUc7SUFDSCxHQUFHO0lBQ0gsR0FBRztJQUNILElBQUk7SUFDSixJQUFJO0lBQ0osSUFBSTtJQUNKLElBQUk7SUFDSixJQUFJO0FBQ047QUFFZSxlQUFlQyxRQUFRQyxHQUFtQixFQUFFQyxHQUFvQjtJQUM3RSxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNakIsZ0VBQWdCQSxDQUFDZSxLQUFLQyxLQUFLZix3REFBV0E7UUFFNUQsSUFBSSxDQUFDZ0IsU0FBU0MsTUFBTTtZQUNsQixPQUFPRixJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWU7UUFDdEQ7UUFFQSwwRUFBMEU7UUFDMUUsTUFBTUMsVUFBVSxRQUFTSixJQUFJLENBQVNJLE9BQU87UUFFN0MsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRSxHQUFHdEIseURBQWVBLENBQUN1QixHQUFHO1FBRTlDLElBQUksQ0FBQ0QsU0FBUyxDQUFDRCxTQUFTO1lBQ3RCRyxRQUFRTCxLQUFLLENBQUM7WUFDZCxPQUFPTCxJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQTRCO1FBQ25FO1FBRUEsSUFBSU4sSUFBSVksTUFBTSxLQUFLLE9BQU87WUFDeEIsSUFBSTtnQkFDRiw2QkFBNkI7Z0JBQzdCLElBQUksQ0FBQ0gsT0FBTztvQkFDVkUsUUFBUUwsS0FBSyxDQUFDO29CQUNkLE9BQU9MLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7d0JBQzFCQyxPQUFPO3dCQUNQTyxTQUFTO29CQUNYO2dCQUNGO2dCQUVBLElBQUksQ0FBQ0wsU0FBUztvQkFDWkcsUUFBUUwsS0FBSyxDQUFDO29CQUNkLE9BQU9MLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7d0JBQzFCQyxPQUFPO3dCQUNQTyxTQUFTO29CQUNYO2dCQUNGO2dCQUVBLG1DQUFtQztnQkFDbkNGLFFBQVFHLEdBQUcsQ0FBQyxpQ0FBaUM7b0JBQzNDRixRQUFRWixJQUFJWSxNQUFNO29CQUNsQkcsT0FBT2YsSUFBSWUsS0FBSztvQkFDaEJDLFNBQVNoQixJQUFJZ0IsT0FBTztvQkFDcEJSO29CQUNBUyxlQUFlLENBQUMsQ0FBQ1I7Z0JBQ25CO2dCQUVBLHlEQUF5RDtnQkFDekQsTUFBTVMsZUFBZTtvQkFDbkJOLFFBQVE7b0JBQ1JJLFNBQVM7d0JBQ1AsaUJBQWlCLENBQUMsSUFBSSxFQUFFUCxPQUFPO3dCQUMvQixnQkFBZ0I7b0JBQ2xCO2dCQUNGO2dCQUVBRSxRQUFRRyxHQUFHLENBQUMsMkNBQTJDO29CQUNyREssS0FBSyxDQUFDLG1DQUFtQyxFQUFFWCxRQUFRLFNBQVMsQ0FBQztvQkFDN0RRLFNBQVM7d0JBQ1BJLGVBQWU7d0JBQ2YsZ0JBQWdCO29CQUNsQjtnQkFDRjtnQkFFQSxxREFBcUQ7Z0JBQ3JELE1BQU1DLDJCQUEyQixNQUFNQyxNQUFNLENBQUMsbUNBQW1DLEVBQUVkLFNBQVMsRUFBRTtvQkFDNUZJLFFBQVE7b0JBQ1JJLFNBQVM7d0JBQ1AsaUJBQWlCLENBQUMsSUFBSSxFQUFFUCxPQUFPO3dCQUMvQixnQkFBZ0I7b0JBQ2xCO2dCQUNGO2dCQUVBLElBQUksQ0FBQ1kseUJBQXlCRSxFQUFFLEVBQUU7b0JBQ2hDLE1BQU1DLHVCQUF1QixNQUFNSCx5QkFBeUJJLElBQUk7b0JBQ2hFZCxRQUFRTCxLQUFLLENBQUMsaUNBQWlDO3dCQUM3Q0YsUUFBUWlCLHlCQUF5QmpCLE1BQU07d0JBQ3ZDc0IsWUFBWUwseUJBQXlCSyxVQUFVO3dCQUMvQ0MsTUFBTUg7b0JBQ1I7b0JBRUEsT0FBT3ZCLElBQUlHLE1BQU0sQ0FBQ2lCLHlCQUF5QmpCLE1BQU0sRUFBRUMsSUFBSSxDQUFDO3dCQUN0REMsT0FBTzt3QkFDUEYsUUFBUWlCLHlCQUF5QmpCLE1BQU07d0JBQ3ZDUyxTQUFTVztvQkFDWDtnQkFDRjtnQkFFQSxNQUFNSSxXQUFXLE1BQU1OLE1BQU0sQ0FBQyxtQ0FBbUMsRUFBRWQsUUFBUSxTQUFTLENBQUMsRUFBRVU7Z0JBRXZGLDJCQUEyQjtnQkFDM0JQLFFBQVFHLEdBQUcsQ0FBQyx5QkFBeUI7b0JBQ25DVixRQUFRd0IsU0FBU3hCLE1BQU07b0JBQ3ZCc0IsWUFBWUUsU0FBU0YsVUFBVTtvQkFDL0JWLFNBQVNhLE9BQU9DLFdBQVcsQ0FBQ0YsU0FBU1osT0FBTyxDQUFDZSxPQUFPO2dCQUN0RDtnQkFFQSxJQUFJLENBQUNILFNBQVNMLEVBQUUsRUFBRTtvQkFDaEIsa0NBQWtDO29CQUNsQyxJQUFJUztvQkFDSixJQUFJO3dCQUNGQSxZQUFZLE1BQU1KLFNBQVNILElBQUk7b0JBQ2pDLEVBQUUsT0FBT1EsWUFBWTt3QkFDbkJELFlBQVk7b0JBQ2Q7b0JBRUFyQixRQUFRTCxLQUFLLENBQUMsc0JBQXNCO3dCQUNsQ0YsUUFBUXdCLFNBQVN4QixNQUFNO3dCQUN2QnNCLFlBQVlFLFNBQVNGLFVBQVU7d0JBQy9CQyxNQUFNSztvQkFDUjtvQkFFQSxPQUFPL0IsSUFBSUcsTUFBTSxDQUFDd0IsU0FBU3hCLE1BQU0sRUFBRUMsSUFBSSxDQUFDO3dCQUN0Q0MsT0FBTzt3QkFDUEYsUUFBUXdCLFNBQVN4QixNQUFNO3dCQUN2QlMsU0FBU21CO29CQUNYO2dCQUNGO2dCQUVBLE1BQU1FLFdBQVcsTUFBTU4sU0FBU3ZCLElBQUk7Z0JBRXBDLGlDQUFpQztnQkFDakNNLFFBQVFHLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFb0IsU0FBU0MsTUFBTSxDQUFDLFNBQVMsQ0FBQztnQkFFOUQsOENBQThDO2dCQUM5QyxNQUFNQyxvQkFBb0JGLFNBQVNHLEdBQUcsQ0FBQ0MsQ0FBQUE7b0JBQ3JDLHNCQUFzQjtvQkFDdEIsTUFBTUMsbUJBQW1CO3dCQUN2QkMsSUFBSUYsUUFBUUUsRUFBRTt3QkFDZEMsTUFBTUgsUUFBUUcsSUFBSTt3QkFDbEJDLE1BQU01QyxrQkFBa0IsQ0FBQ3dDLFFBQVFJLElBQUksQ0FBQyxJQUFJO3dCQUMxQ0MsVUFBVUwsUUFBUUssUUFBUTtvQkFDNUI7b0JBRUEsNkJBQTZCO29CQUM3QixJQUFJTCxRQUFRTSxTQUFTLEVBQUU7d0JBQ3JCTCxpQkFBaUJLLFNBQVMsR0FBR04sUUFBUU0sU0FBUztvQkFDaEQ7b0JBRUEsK0NBQStDO29CQUMvQ0wsaUJBQWlCTSxRQUFRLEdBQUdQLFFBQVFJLElBQUk7b0JBRXhDLGtEQUFrRDtvQkFDbEQsSUFBSUosUUFBUUksSUFBSSxLQUFLdEQsY0FBY0MsVUFBVSxFQUFFO3dCQUM3Q2tELGlCQUFpQk8sS0FBSyxHQUFHUixRQUFRUSxLQUFLO3dCQUN0Q1AsaUJBQWlCUSxJQUFJLEdBQUdULFFBQVFTLElBQUk7d0JBQ3BDUixpQkFBaUJTLG1CQUFtQixHQUFHVixRQUFRVSxtQkFBbUI7b0JBQ3BFLE9BQU8sSUFBSVYsUUFBUUksSUFBSSxLQUFLdEQsY0FBY0UsV0FBVyxFQUFFO3dCQUNyRGlELGlCQUFpQlUsT0FBTyxHQUFHWCxRQUFRVyxPQUFPO3dCQUMxQ1YsaUJBQWlCVyxVQUFVLEdBQUdaLFFBQVFZLFVBQVU7b0JBQ2xEO29CQUVBLE9BQU9YO2dCQUNUO2dCQUVBLE9BQU90QyxJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDK0I7WUFDOUIsRUFBRSxPQUFPOUIsT0FBTztnQkFDZCw4QkFBOEI7Z0JBQzlCSyxRQUFRTCxLQUFLLENBQUMsMENBQTBDO29CQUN0RDZDLFNBQVM3QyxNQUFNNkMsT0FBTztvQkFDdEJDLE9BQU85QyxNQUFNOEMsS0FBSztvQkFDbEI1QztvQkFDQVMsZUFBZSxDQUFDLENBQUNSO2dCQUNuQjtnQkFFQSxPQUFPUixJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUMxQkMsT0FBTztvQkFDUE8sU0FBU1AsTUFBTTZDLE9BQU87b0JBQ3RCM0MsU0FBU0EsVUFBVSxhQUFhO29CQUNoQ1MsZUFBZSxDQUFDLENBQUNSO2dCQUNuQjtZQUNGO1FBQ0Y7UUFFQSxJQUFJVCxJQUFJWSxNQUFNLEtBQUssUUFBUTtZQUN6QiwrQ0FBK0M7WUFDL0MsSUFBSSxDQUFDTCxTQUFTO2dCQUNaLE9BQU9OLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7b0JBQUVDLE9BQU87Z0JBQW9DO1lBQzNFO1lBRUEsSUFBSTtnQkFDRixNQUFNLEVBQ0ptQyxJQUFJLEVBQ0pDLElBQUksRUFDSkksS0FBSyxFQUNMQyxJQUFJLEVBQ0pFLE9BQU8sRUFDUEksU0FBUyxFQUNUQyxNQUFNLEVBQ05YLFFBQVEsRUFDUlksZ0JBQWdCLEVBQ2pCLEdBQUd2RCxJQUFJMkIsSUFBSTtnQkFFWmhCLFFBQVFHLEdBQUcsQ0FBQyxzQ0FBc0NkLElBQUkyQixJQUFJO2dCQUUxRCwyQkFBMkI7Z0JBQzNCLElBQUksQ0FBQ2MsUUFBUyxPQUFPQyxTQUFTLFlBQVksT0FBT0EsU0FBUyxVQUFXO29CQUNuRS9CLFFBQVFHLEdBQUcsQ0FBQyxzQkFBc0I7d0JBQUUyQjt3QkFBTUM7b0JBQUs7b0JBQy9DLE9BQU96QyxJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO3dCQUFFQyxPQUFPO29CQUE2QjtnQkFDcEU7Z0JBRUEsMkNBQTJDO2dCQUMzQyxJQUFJa0QsY0FBYyxPQUFPZCxTQUFTLFdBQVdBLE9BQU90RCxhQUFhLENBQUNzRCxLQUFLO2dCQUN2RS9CLFFBQVFHLEdBQUcsQ0FBQyw0QkFBNEI7b0JBQUUyQyxVQUFVZjtvQkFBTWdCLFdBQVdGO2dCQUFZO2dCQUNqRixJQUFJLE9BQU9BLGdCQUFnQixVQUFVO29CQUNuQyxPQUFPdkQsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQzt3QkFBRUMsT0FBTztvQkFBdUI7Z0JBQzlEO2dCQUVBLHFDQUFxQztnQkFDckMsTUFBTXFELGNBQW1CO29CQUN2QmxCO29CQUNBQyxNQUFNYztvQkFDTmIsVUFBVUEsWUFBWTtnQkFDeEI7Z0JBRUEsK0JBQStCO2dCQUMvQixJQUFJYSxnQkFBZ0JwRSxjQUFjQyxVQUFVLEVBQUU7b0JBQzVDLElBQUl5RCxPQUFPYSxZQUFZYixLQUFLLEdBQUdBO29CQUMvQmEsWUFBWVosSUFBSSxHQUFHYSxRQUFRYjtvQkFDM0IsSUFBSSxPQUFPUSxxQkFBcUIsWUFBWSxDQUFDTSxNQUFNTixtQkFBbUI7d0JBQ3BFSSxZQUFZWCxtQkFBbUIsR0FBR087b0JBQ3BDO2dCQUNGLE9BQU8sSUFBSUMsZ0JBQWdCcEUsY0FBY0UsV0FBVyxFQUFFO29CQUNwRCxJQUFJLE9BQU8yRCxZQUFZLFlBQVksQ0FBQ1ksTUFBTVosVUFBVTt3QkFDbERVLFlBQVlWLE9BQU8sR0FBR0E7b0JBQ3hCO29CQUNBLElBQUksT0FBT0ksY0FBYyxZQUFZLENBQUNRLE1BQU1SLFlBQVk7d0JBQ3RETSxZQUFZVCxVQUFVLEdBQUdHO29CQUMzQjtnQkFDRjtnQkFFQSxtQ0FBbUM7Z0JBQ25DLElBQUlDLFVBQVVFLGdCQUFnQnBFLGNBQWNHLGNBQWMsRUFBRTtvQkFDMURvRSxZQUFZZixTQUFTLEdBQUdVO2dCQUMxQjtnQkFFQTNDLFFBQVFHLEdBQUcsQ0FBQywrQkFBK0I2QztnQkFDM0MsTUFBTS9CLFdBQVcsTUFBTU4sTUFBTSxDQUFDLG1DQUFtQyxFQUFFZCxRQUFRLFNBQVMsQ0FBQyxFQUFFO29CQUNyRkksUUFBUTtvQkFDUkksU0FBUzt3QkFDUEksZUFBZSxDQUFDLElBQUksRUFBRVgsT0FBTzt3QkFDN0IsZ0JBQWdCO29CQUNsQjtvQkFDQWtCLE1BQU1tQyxLQUFLQyxTQUFTLENBQUNKO2dCQUN2QjtnQkFFQSxJQUFJLENBQUMvQixTQUFTTCxFQUFFLEVBQUU7b0JBQ2hCLElBQUlqQjtvQkFDSixJQUFJO3dCQUNGQSxRQUFRLE1BQU1zQixTQUFTdkIsSUFBSTtvQkFDN0IsRUFBRSxPQUFNO3dCQUNOQyxRQUFRLE1BQU1zQixTQUFTSCxJQUFJO29CQUM3QjtvQkFDQWQsUUFBUUwsS0FBSyxDQUFDLHNCQUFzQkE7b0JBQ3BDLE9BQU9MLElBQUlHLE1BQU0sQ0FBQ3dCLFNBQVN4QixNQUFNLEVBQUVDLElBQUksQ0FBQ0M7Z0JBQzFDO2dCQUVBLE1BQU0wRCxhQUFhLE1BQU1wQyxTQUFTdkIsSUFBSTtnQkFDdEMsT0FBT0osSUFBSUcsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQzJEO1lBQzlCLEVBQUUsT0FBTzFELE9BQU87Z0JBQ2RLLFFBQVFMLEtBQUssQ0FBQywyQkFBMkJBO2dCQUN6QyxPQUFPTCxJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUEyQjtZQUNsRTtRQUNGO1FBRUEsSUFBSU4sSUFBSVksTUFBTSxLQUFLLFNBQVM7WUFDMUIsOENBQThDO1lBQzlDLElBQUksQ0FBQ0wsU0FBUztnQkFDWixPQUFPTixJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUFvQztZQUMzRTtZQUVBLElBQUk7Z0JBQ0YsTUFBTTJELFlBQVlqRSxJQUFJZSxLQUFLLENBQUNrRCxTQUFTO2dCQUNyQyxJQUFJLENBQUNBLFdBQVc7b0JBQ2QsT0FBT2hFLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7d0JBQUVDLE9BQU87b0JBQXlCO2dCQUNoRTtnQkFFQSxNQUFNc0IsV0FBVyxNQUFNTixNQUFNLENBQUMscUNBQXFDLEVBQUUyQyxXQUFXLEVBQUU7b0JBQ2hGckQsUUFBUTtvQkFDUkksU0FBUzt3QkFDUEksZUFBZSxDQUFDLElBQUksRUFBRVgsT0FBTzt3QkFDN0IsZ0JBQWdCO29CQUNsQjtvQkFDQWtCLE1BQU1tQyxLQUFLQyxTQUFTLENBQUMvRCxJQUFJMkIsSUFBSTtnQkFDL0I7Z0JBRUEsSUFBSSxDQUFDQyxTQUFTTCxFQUFFLEVBQUU7b0JBQ2hCLElBQUlqQjtvQkFDSixJQUFJO3dCQUNGQSxRQUFRLE1BQU1zQixTQUFTdkIsSUFBSTtvQkFDN0IsRUFBRSxPQUFNO3dCQUNOQyxRQUFRLE1BQU1zQixTQUFTSCxJQUFJO29CQUM3QjtvQkFDQWQsUUFBUUwsS0FBSyxDQUFDLHNCQUFzQkE7b0JBQ3BDLE9BQU9MLElBQUlHLE1BQU0sQ0FBQ3dCLFNBQVN4QixNQUFNLEVBQUVDLElBQUksQ0FBQ0M7Z0JBQzFDO2dCQUVBLE1BQU00RCxpQkFBaUIsTUFBTXRDLFNBQVN2QixJQUFJO2dCQUMxQyxPQUFPSixJQUFJRyxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDNkQ7WUFDOUIsRUFBRSxPQUFPNUQsT0FBTztnQkFDZEssUUFBUUwsS0FBSyxDQUFDLDJCQUEyQkE7Z0JBQ3pDLE9BQU9MLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7b0JBQUVDLE9BQU87Z0JBQTJCO1lBQ2xFO1FBQ0Y7UUFFQSxJQUFJTixJQUFJWSxNQUFNLEtBQUssVUFBVTtZQUMzQiwrQ0FBK0M7WUFDL0MsSUFBSSxDQUFDTCxTQUFTO2dCQUNaLE9BQU9OLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7b0JBQUVDLE9BQU87Z0JBQW9DO1lBQzNFO1lBRUEsSUFBSTtnQkFDRixNQUFNMkQsWUFBWWpFLElBQUllLEtBQUssQ0FBQ2tELFNBQVM7Z0JBQ3JDLElBQUksQ0FBQ0EsV0FBVztvQkFDZCxPQUFPaEUsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQzt3QkFBRUMsT0FBTztvQkFBeUI7Z0JBQ2hFO2dCQUVBLE1BQU1zQixXQUFXLE1BQU1OLE1BQU0sQ0FBQyxxQ0FBcUMsRUFBRTJDLFdBQVcsRUFBRTtvQkFDaEZyRCxRQUFRO29CQUNSSSxTQUFTO3dCQUNQSSxlQUFlLENBQUMsSUFBSSxFQUFFWCxPQUFPO29CQUMvQjtnQkFDRjtnQkFFQSxJQUFJLENBQUNtQixTQUFTTCxFQUFFLEVBQUU7b0JBQ2hCLElBQUlqQjtvQkFDSixJQUFJO3dCQUNGQSxRQUFRLE1BQU1zQixTQUFTdkIsSUFBSTtvQkFDN0IsRUFBRSxPQUFNO3dCQUNOQyxRQUFRLE1BQU1zQixTQUFTSCxJQUFJO29CQUM3QjtvQkFDQWQsUUFBUUwsS0FBSyxDQUFDLHNCQUFzQkE7b0JBQ3BDLE9BQU9MLElBQUlHLE1BQU0sQ0FBQ3dCLFNBQVN4QixNQUFNLEVBQUVDLElBQUksQ0FBQ0M7Z0JBQzFDO2dCQUVBLE9BQU9MLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7b0JBQUU4QyxTQUFTO2dCQUErQjtZQUN4RSxFQUFFLE9BQU83QyxPQUFPO2dCQUNkSyxRQUFRTCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekMsT0FBT0wsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztvQkFBRUMsT0FBTztnQkFBMkI7WUFDbEU7UUFDRjtRQUVBLE9BQU9MLElBQUlHLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFBRUMsT0FBTztRQUFxQjtJQUM1RCxFQUFFLE9BQU9BLE9BQU87UUFDZEssUUFBUUwsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBT0wsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXdCO0lBQy9EO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXHNyY1xcZGFzaGJvYXJkXFxwYWdlc1xcYXBpXFxkaXNjb3JkXFxjaGFubmVscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAdHMtbm9jaGVja1xyXG5pbXBvcnQgeyBOZXh0QXBpUmVxdWVzdCwgTmV4dEFwaVJlc3BvbnNlIH0gZnJvbSAnbmV4dCc7XHJcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvbmV4dCc7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnLi4vYXV0aC9bLi4ubmV4dGF1dGhdJztcclxuaW1wb3J0IHsgZGFzaGJvYXJkQ29uZmlnIH0gZnJvbSAnLi4vLi4vLi4vY29yZS9jb25maWcnO1xyXG5cclxuLy8gRGlzY29yZCBjaGFubmVsIHR5cGUgbWFwcGluZ1xyXG5jb25zdCBDSEFOTkVMX1RZUEVTID0ge1xyXG4gIEdVSUxEX1RFWFQ6IDAsXHJcbiAgR1VJTERfVk9JQ0U6IDIsXHJcbiAgR1VJTERfQ0FURUdPUlk6IDQsXHJcbiAgR1VJTERfQU5OT1VOQ0VNRU5UOiA1LFxyXG4gIEdVSUxEX0FOTk9VTkNFTUVOVF9USFJFQUQ6IDEwLFxyXG4gIEdVSUxEX1BVQkxJQ19USFJFQUQ6IDExLFxyXG4gIEdVSUxEX1BSSVZBVEVfVEhSRUFEOiAxMixcclxuICBHVUlMRF9TVEFHRV9WT0lDRTogMTMsXHJcbiAgR1VJTERfRk9SVU06IDE1LFxyXG59O1xyXG5cclxuLy8gUmV2ZXJzZSBtYXBwaW5nIGZvciB0eXBlIGNvbnZlcnNpb25cclxuY29uc3QgQ0hBTk5FTF9UWVBFX05BTUVTID0ge1xyXG4gIDA6ICdHVUlMRF9URVhUJyxcclxuICAyOiAnR1VJTERfVk9JQ0UnLFxyXG4gIDQ6ICdHVUlMRF9DQVRFR09SWScsXHJcbiAgNTogJ0dVSUxEX0FOTk9VTkNFTUVOVCcsXHJcbiAgMTA6ICdHVUlMRF9BTk5PVU5DRU1FTlRfVEhSRUFEJyxcclxuICAxMTogJ0dVSUxEX1BVQkxJQ19USFJFQUQnLFxyXG4gIDEyOiAnR1VJTERfUFJJVkFURV9USFJFQUQnLFxyXG4gIDEzOiAnR1VJTERfU1RBR0VfVk9JQ0UnLFxyXG4gIDE1OiAnR1VJTERfRk9SVU0nLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXE6IE5leHRBcGlSZXF1ZXN0LCByZXM6IE5leHRBcGlSZXNwb25zZSkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihyZXEsIHJlcywgYXV0aE9wdGlvbnMpO1xyXG5cclxuICAgIGlmICghc2Vzc2lvbj8udXNlcikge1xyXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDEpLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRm9yIHNlbnNpdGl2ZSBvcGVyYXRpb25zIGxpa2UgY3JlYXRpbmcgY2hhbm5lbHMgd2Ugc3RpbGwgcmVxdWlyZSBhZG1pbi5cclxuICAgIGNvbnN0IGlzQWRtaW4gPSAoc2Vzc2lvbi51c2VyIGFzIGFueSkuaXNBZG1pbjtcclxuXHJcbiAgICBjb25zdCB7IGd1aWxkSWQsIHRva2VuIH0gPSBkYXNoYm9hcmRDb25maWcuYm90O1xyXG4gICAgXHJcbiAgICBpZiAoIXRva2VuIHx8ICFndWlsZElkKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ01pc3NpbmcgYm90IGNvbmZpZ3VyYXRpb24nKTtcclxuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgZXJyb3I6ICdCb3QgY29uZmlndXJhdGlvbiBtaXNzaW5nJyB9KTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAocmVxLm1ldGhvZCA9PT0gJ0dFVCcpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBWYWxpZGF0ZSBib3QgY29uZmlndXJhdGlvblxyXG4gICAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ05vIGJvdCB0b2tlbiBwcm92aWRlZCcpO1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgXHJcbiAgICAgICAgICAgIGVycm9yOiAnQm90IGNvbmZpZ3VyYXRpb24gaW5jb21wbGV0ZScsIFxyXG4gICAgICAgICAgICBkZXRhaWxzOiAnTm8gYm90IHRva2VuIGZvdW5kJyBcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKCFndWlsZElkKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdObyBndWlsZCBJRCBwcm92aWRlZCcpO1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgXHJcbiAgICAgICAgICAgIGVycm9yOiAnQm90IGNvbmZpZ3VyYXRpb24gaW5jb21wbGV0ZScsIFxyXG4gICAgICAgICAgICBkZXRhaWxzOiAnTm8gZ3VpbGQgSUQgZm91bmQnIFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBMb2cgZGV0YWlsZWQgcmVxdWVzdCBpbmZvcm1hdGlvblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDaGFubmVscyBBUEkgUmVxdWVzdCBEZXRhaWxzOicsIHtcclxuICAgICAgICAgIG1ldGhvZDogcmVxLm1ldGhvZCxcclxuICAgICAgICAgIHF1ZXJ5OiByZXEucXVlcnksXHJcbiAgICAgICAgICBoZWFkZXJzOiByZXEuaGVhZGVycyxcclxuICAgICAgICAgIGd1aWxkSWQsXHJcbiAgICAgICAgICB0b2tlblByb3ZpZGVkOiAhIXRva2VuXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIEF0dGVtcHQgdG8gZmV0Y2ggY2hhbm5lbHMgd2l0aCBkZXRhaWxlZCBlcnJvciBoYW5kbGluZ1xyXG4gICAgICAgIGNvbnN0IGZldGNoT3B0aW9ucyA9IHtcclxuICAgICAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJvdCAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBEaXNjb3JkIGNoYW5uZWxzIHdpdGggb3B0aW9uczonLCB7XHJcbiAgICAgICAgICB1cmw6IGBodHRwczovL2Rpc2NvcmQuY29tL2FwaS92MTAvZ3VpbGRzLyR7Z3VpbGRJZH0vY2hhbm5lbHNgLFxyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiAnQm90IFtSRURBQ1RFRF0nLFxyXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBWYWxpZGF0ZSBib3QgcGVybWlzc2lvbnMgYmVmb3JlIG1ha2luZyB0aGUgcmVxdWVzdFxyXG4gICAgICAgIGNvbnN0IHBlcm1pc3Npb25zQ2hlY2tSZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2Rpc2NvcmQuY29tL2FwaS92MTAvZ3VpbGRzLyR7Z3VpbGRJZH1gLCB7XHJcbiAgICAgICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCb3QgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFwZXJtaXNzaW9uc0NoZWNrUmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGNvbnN0IHBlcm1pc3Npb25zRXJyb3JCb2R5ID0gYXdhaXQgcGVybWlzc2lvbnNDaGVja1Jlc3BvbnNlLnRleHQoKTtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0JvdCBQZXJtaXNzaW9ucyBDaGVjayBGYWlsZWQ6Jywge1xyXG4gICAgICAgICAgICBzdGF0dXM6IHBlcm1pc3Npb25zQ2hlY2tSZXNwb25zZS5zdGF0dXMsXHJcbiAgICAgICAgICAgIHN0YXR1c1RleHQ6IHBlcm1pc3Npb25zQ2hlY2tSZXNwb25zZS5zdGF0dXNUZXh0LFxyXG4gICAgICAgICAgICBib2R5OiBwZXJtaXNzaW9uc0Vycm9yQm9keVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMocGVybWlzc2lvbnNDaGVja1Jlc3BvbnNlLnN0YXR1cykuanNvbih7XHJcbiAgICAgICAgICAgIGVycm9yOiAnQm90IHBlcm1pc3Npb25zIGNoZWNrIGZhaWxlZCcsXHJcbiAgICAgICAgICAgIHN0YXR1czogcGVybWlzc2lvbnNDaGVja1Jlc3BvbnNlLnN0YXR1cyxcclxuICAgICAgICAgICAgZGV0YWlsczogcGVybWlzc2lvbnNFcnJvckJvZHlcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9kaXNjb3JkLmNvbS9hcGkvdjEwL2d1aWxkcy8ke2d1aWxkSWR9L2NoYW5uZWxzYCwgZmV0Y2hPcHRpb25zKTtcclxuXHJcbiAgICAgICAgLy8gTG9nIHJhdyByZXNwb25zZSBkZXRhaWxzXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0Rpc2NvcmQgQVBJIFJlc3BvbnNlOicsIHtcclxuICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxyXG4gICAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcclxuICAgICAgICAgIGhlYWRlcnM6IE9iamVjdC5mcm9tRW50cmllcyhyZXNwb25zZS5oZWFkZXJzLmVudHJpZXMoKSlcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgLy8gQXR0ZW1wdCB0byBwYXJzZSBlcnJvciByZXNwb25zZVxyXG4gICAgICAgICAgbGV0IGVycm9yQm9keTtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGVycm9yQm9keSA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcclxuICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcclxuICAgICAgICAgICAgZXJyb3JCb2R5ID0gJ1VuYWJsZSB0byBwYXJzZSBlcnJvciByZXNwb25zZSc7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRGlzY29yZCBBUEkgRXJyb3I6Jywge1xyXG4gICAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcclxuICAgICAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcclxuICAgICAgICAgICAgYm9keTogZXJyb3JCb2R5XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyhyZXNwb25zZS5zdGF0dXMpLmpzb24oe1xyXG4gICAgICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBjaGFubmVscyBmcm9tIERpc2NvcmQnLFxyXG4gICAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcclxuICAgICAgICAgICAgZGV0YWlsczogZXJyb3JCb2R5XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGNoYW5uZWxzID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgICAgICAvLyBMb2cgbnVtYmVyIG9mIGNoYW5uZWxzIGZldGNoZWRcclxuICAgICAgICBjb25zb2xlLmxvZyhgU3VjY2Vzc2Z1bGx5IGZldGNoZWQgJHtjaGFubmVscy5sZW5ndGh9IGNoYW5uZWxzYCk7XHJcblxyXG4gICAgICAgIC8vIEZvcm1hdCBjaGFubmVscyBiYXNlZCBvbiB0aGUgcmVxdWVzdCBzb3VyY2VcclxuICAgICAgICBjb25zdCBmb3JtYXR0ZWRDaGFubmVscyA9IGNoYW5uZWxzLm1hcChjaGFubmVsID0+IHtcclxuICAgICAgICAgIC8vIEJhc2UgY2hhbm5lbCBmb3JtYXRcclxuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZENoYW5uZWwgPSB7XHJcbiAgICAgICAgICAgIGlkOiBjaGFubmVsLmlkLFxyXG4gICAgICAgICAgICBuYW1lOiBjaGFubmVsLm5hbWUsXHJcbiAgICAgICAgICAgIHR5cGU6IENIQU5ORUxfVFlQRV9OQU1FU1tjaGFubmVsLnR5cGVdIHx8ICd1bmtub3duJyxcclxuICAgICAgICAgICAgcG9zaXRpb246IGNoYW5uZWwucG9zaXRpb24sXHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIC8vIEFkZCBwYXJlbnRfaWQgaWYgaXQgZXhpc3RzXHJcbiAgICAgICAgICBpZiAoY2hhbm5lbC5wYXJlbnRfaWQpIHtcclxuICAgICAgICAgICAgZm9ybWF0dGVkQ2hhbm5lbC5wYXJlbnRfaWQgPSBjaGFubmVsLnBhcmVudF9pZDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBBZGQgcmF3IHR5cGUgZm9yIHRoZSBjaGFubmVsIG1hbmFnZW1lbnQgcGFnZVxyXG4gICAgICAgICAgZm9ybWF0dGVkQ2hhbm5lbC5yYXdfdHlwZSA9IGNoYW5uZWwudHlwZTtcclxuXHJcbiAgICAgICAgICAvLyBBZGQgYWRkaXRpb25hbCBwcm9wZXJ0aWVzIGJhc2VkIG9uIGNoYW5uZWwgdHlwZVxyXG4gICAgICAgICAgaWYgKGNoYW5uZWwudHlwZSA9PT0gQ0hBTk5FTF9UWVBFUy5HVUlMRF9URVhUKSB7IC8vIFRleHQgY2hhbm5lbFxyXG4gICAgICAgICAgICBmb3JtYXR0ZWRDaGFubmVsLnRvcGljID0gY2hhbm5lbC50b3BpYztcclxuICAgICAgICAgICAgZm9ybWF0dGVkQ2hhbm5lbC5uc2Z3ID0gY2hhbm5lbC5uc2Z3O1xyXG4gICAgICAgICAgICBmb3JtYXR0ZWRDaGFubmVsLnJhdGVfbGltaXRfcGVyX3VzZXIgPSBjaGFubmVsLnJhdGVfbGltaXRfcGVyX3VzZXI7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKGNoYW5uZWwudHlwZSA9PT0gQ0hBTk5FTF9UWVBFUy5HVUlMRF9WT0lDRSkgeyAvLyBWb2ljZSBjaGFubmVsXHJcbiAgICAgICAgICAgIGZvcm1hdHRlZENoYW5uZWwuYml0cmF0ZSA9IGNoYW5uZWwuYml0cmF0ZTtcclxuICAgICAgICAgICAgZm9ybWF0dGVkQ2hhbm5lbC51c2VyX2xpbWl0ID0gY2hhbm5lbC51c2VyX2xpbWl0O1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybiBmb3JtYXR0ZWRDaGFubmVsO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cygyMDApLmpzb24oZm9ybWF0dGVkQ2hhbm5lbHMpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIC8vIENvbXByZWhlbnNpdmUgZXJyb3IgbG9nZ2luZ1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0NvbXByZWhlbnNpdmUgZXJyb3IgZmV0Y2hpbmcgY2hhbm5lbHM6Jywge1xyXG4gICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICAgIHN0YWNrOiBlcnJvci5zdGFjayxcclxuICAgICAgICAgIGd1aWxkSWQsXHJcbiAgICAgICAgICB0b2tlblByb3ZpZGVkOiAhIXRva2VuXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDUwMCkuanNvbih7IFxyXG4gICAgICAgICAgZXJyb3I6ICdVbmV4cGVjdGVkIGVycm9yIGZldGNoaW5nIGNoYW5uZWxzJywgXHJcbiAgICAgICAgICBkZXRhaWxzOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICAgICAgZ3VpbGRJZDogZ3VpbGRJZCA/ICdQcm92aWRlZCcgOiAnTWlzc2luZycsXHJcbiAgICAgICAgICB0b2tlblByb3ZpZGVkOiAhIXRva2VuXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAocmVxLm1ldGhvZCA9PT0gJ1BPU1QnKSB7XHJcbiAgICAgIC8vIENyZWF0aW5nIGNoYW5uZWxzIHJlcXVpcmVzIGFkbWluIHBlcm1pc3Npb25zXHJcbiAgICAgIGlmICghaXNBZG1pbikge1xyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMykuanNvbih7IGVycm9yOiAnRm9yYmlkZGVuIC0gQWRtaW4gYWNjZXNzIHJlcXVpcmVkJyB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB7XHJcbiAgICAgICAgICBuYW1lLFxyXG4gICAgICAgICAgdHlwZSxcclxuICAgICAgICAgIHRvcGljLFxyXG4gICAgICAgICAgbnNmdyxcclxuICAgICAgICAgIGJpdHJhdGUsXHJcbiAgICAgICAgICB1c2VyTGltaXQsXHJcbiAgICAgICAgICBwYXJlbnQsXHJcbiAgICAgICAgICBwb3NpdGlvbixcclxuICAgICAgICAgIHJhdGVMaW1pdFBlclVzZXIsXHJcbiAgICAgICAgfSA9IHJlcS5ib2R5O1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnUmVjZWl2ZWQgY2hhbm5lbCBjcmVhdGlvbiByZXF1ZXN0OicsIHJlcS5ib2R5KTtcclxuXHJcbiAgICAgICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXHJcbiAgICAgICAgaWYgKCFuYW1lIHx8ICh0eXBlb2YgdHlwZSAhPT0gJ251bWJlcicgJiYgdHlwZW9mIHR5cGUgIT09ICdzdHJpbmcnKSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYXRpb24gZmFpbGVkOicsIHsgbmFtZSwgdHlwZSB9KTtcclxuICAgICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IGVycm9yOiAnTmFtZSBhbmQgdHlwZSBhcmUgcmVxdWlyZWQnIH0pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQ29udmVydCBzdHJpbmcgdHlwZSB0byBudW1lcmljIGlmIG5lZWRlZFxyXG4gICAgICAgIGxldCBudW1lcmljVHlwZSA9IHR5cGVvZiB0eXBlID09PSAnbnVtYmVyJyA/IHR5cGUgOiBDSEFOTkVMX1RZUEVTW3R5cGVdO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDaGFubmVsIHR5cGUgY29udmVyc2lvbjonLCB7IG9yaWdpbmFsOiB0eXBlLCBjb252ZXJ0ZWQ6IG51bWVyaWNUeXBlIH0pO1xyXG4gICAgICAgIGlmICh0eXBlb2YgbnVtZXJpY1R5cGUgIT09ICdudW1iZXInKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oeyBlcnJvcjogJ0ludmFsaWQgY2hhbm5lbCB0eXBlJyB9KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFByZXBhcmUgY2hhbm5lbCBkYXRhIGJhc2VkIG9uIHR5cGVcclxuICAgICAgICBjb25zdCBjaGFubmVsRGF0YTogYW55ID0ge1xyXG4gICAgICAgICAgbmFtZSxcclxuICAgICAgICAgIHR5cGU6IG51bWVyaWNUeXBlLFxyXG4gICAgICAgICAgcG9zaXRpb246IHBvc2l0aW9uIHx8IDAsXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgLy8gQWRkIHR5cGUtc3BlY2lmaWMgcHJvcGVydGllc1xyXG4gICAgICAgIGlmIChudW1lcmljVHlwZSA9PT0gQ0hBTk5FTF9UWVBFUy5HVUlMRF9URVhUKSB7XHJcbiAgICAgICAgICBpZiAodG9waWMpIGNoYW5uZWxEYXRhLnRvcGljID0gdG9waWM7XHJcbiAgICAgICAgICBjaGFubmVsRGF0YS5uc2Z3ID0gQm9vbGVhbihuc2Z3KTtcclxuICAgICAgICAgIGlmICh0eXBlb2YgcmF0ZUxpbWl0UGVyVXNlciA9PT0gJ251bWJlcicgJiYgIWlzTmFOKHJhdGVMaW1pdFBlclVzZXIpKSB7XHJcbiAgICAgICAgICAgIGNoYW5uZWxEYXRhLnJhdGVfbGltaXRfcGVyX3VzZXIgPSByYXRlTGltaXRQZXJVc2VyO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSBpZiAobnVtZXJpY1R5cGUgPT09IENIQU5ORUxfVFlQRVMuR1VJTERfVk9JQ0UpIHtcclxuICAgICAgICAgIGlmICh0eXBlb2YgYml0cmF0ZSA9PT0gJ251bWJlcicgJiYgIWlzTmFOKGJpdHJhdGUpKSB7XHJcbiAgICAgICAgICAgIGNoYW5uZWxEYXRhLmJpdHJhdGUgPSBiaXRyYXRlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaWYgKHR5cGVvZiB1c2VyTGltaXQgPT09ICdudW1iZXInICYmICFpc05hTih1c2VyTGltaXQpKSB7XHJcbiAgICAgICAgICAgIGNoYW5uZWxEYXRhLnVzZXJfbGltaXQgPSB1c2VyTGltaXQ7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBBZGQgcGFyZW50IGNhdGVnb3J5IGlmIHNwZWNpZmllZFxyXG4gICAgICAgIGlmIChwYXJlbnQgJiYgbnVtZXJpY1R5cGUgIT09IENIQU5ORUxfVFlQRVMuR1VJTERfQ0FURUdPUlkpIHtcclxuICAgICAgICAgIGNoYW5uZWxEYXRhLnBhcmVudF9pZCA9IHBhcmVudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDcmVhdGluZyBjaGFubmVsIHdpdGggZGF0YTonLCBjaGFubmVsRGF0YSk7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9kaXNjb3JkLmNvbS9hcGkvdjEwL2d1aWxkcy8ke2d1aWxkSWR9L2NoYW5uZWxzYCwge1xyXG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCb3QgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGNoYW5uZWxEYXRhKSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgbGV0IGVycm9yO1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgICB9IGNhdGNoIHtcclxuICAgICAgICAgICAgZXJyb3IgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdEaXNjb3JkIEFQSSBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyhyZXNwb25zZS5zdGF0dXMpLmpzb24oZXJyb3IpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgbmV3Q2hhbm5lbCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cygyMDEpLmpzb24obmV3Q2hhbm5lbCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY2hhbm5lbDonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIGNoYW5uZWwnIH0pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHJlcS5tZXRob2QgPT09ICdQQVRDSCcpIHtcclxuICAgICAgLy8gRWRpdGluZyBjaGFubmVscyByZXF1aXJlcyBhZG1pbiBwZXJtaXNzaW9uc1xyXG4gICAgICBpZiAoIWlzQWRtaW4pIHtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDMpLmpzb24oeyBlcnJvcjogJ0ZvcmJpZGRlbiAtIEFkbWluIGFjY2VzcyByZXF1aXJlZCcgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgY2hhbm5lbElkID0gcmVxLnF1ZXJ5LmNoYW5uZWxJZCBhcyBzdHJpbmc7XHJcbiAgICAgICAgaWYgKCFjaGFubmVsSWQpIHtcclxuICAgICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IGVycm9yOiAnQ2hhbm5lbCBJRCBpcyByZXF1aXJlZCcgfSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2Rpc2NvcmQuY29tL2FwaS92MTAvY2hhbm5lbHMvJHtjaGFubmVsSWR9YCwge1xyXG4gICAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQm90ICR7dG9rZW59YCxcclxuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXEuYm9keSksXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGxldCBlcnJvcjtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgfSBjYXRjaCB7XHJcbiAgICAgICAgICAgIGVycm9yID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRGlzY29yZCBBUEkgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMocmVzcG9uc2Uuc3RhdHVzKS5qc29uKGVycm9yKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRDaGFubmVsID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDIwMCkuanNvbih1cGRhdGVkQ2hhbm5lbCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgY2hhbm5lbDonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIGNoYW5uZWwnIH0pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHJlcS5tZXRob2QgPT09ICdERUxFVEUnKSB7XHJcbiAgICAgIC8vIERlbGV0aW5nIGNoYW5uZWxzIHJlcXVpcmVzIGFkbWluIHBlcm1pc3Npb25zXHJcbiAgICAgIGlmICghaXNBZG1pbikge1xyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMykuanNvbih7IGVycm9yOiAnRm9yYmlkZGVuIC0gQWRtaW4gYWNjZXNzIHJlcXVpcmVkJyB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBjaGFubmVsSWQgPSByZXEucXVlcnkuY2hhbm5lbElkIGFzIHN0cmluZztcclxuICAgICAgICBpZiAoIWNoYW5uZWxJZCkge1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAwKS5qc29uKHsgZXJyb3I6ICdDaGFubmVsIElEIGlzIHJlcXVpcmVkJyB9KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vZGlzY29yZC5jb20vYXBpL3YxMC9jaGFubmVscy8ke2NoYW5uZWxJZH1gLCB7XHJcbiAgICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQm90ICR7dG9rZW59YCxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGxldCBlcnJvcjtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgfSBjYXRjaCB7XHJcbiAgICAgICAgICAgIGVycm9yID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRGlzY29yZCBBUEkgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMocmVzcG9uc2Uuc3RhdHVzKS5qc29uKGVycm9yKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDIwMCkuanNvbih7IG1lc3NhZ2U6ICdDaGFubmVsIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBjaGFubmVsOicsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgY2hhbm5lbCcgfSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDUpLmpzb24oeyBlcnJvcjogJ01ldGhvZCBub3QgYWxsb3dlZCcgfSk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGNoYW5uZWwgaGFuZGxlcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSk7XHJcbiAgfVxyXG59ICJdLCJuYW1lcyI6WyJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJkYXNoYm9hcmRDb25maWciLCJDSEFOTkVMX1RZUEVTIiwiR1VJTERfVEVYVCIsIkdVSUxEX1ZPSUNFIiwiR1VJTERfQ0FURUdPUlkiLCJHVUlMRF9BTk5PVU5DRU1FTlQiLCJHVUlMRF9BTk5PVU5DRU1FTlRfVEhSRUFEIiwiR1VJTERfUFVCTElDX1RIUkVBRCIsIkdVSUxEX1BSSVZBVEVfVEhSRUFEIiwiR1VJTERfU1RBR0VfVk9JQ0UiLCJHVUlMRF9GT1JVTSIsIkNIQU5ORUxfVFlQRV9OQU1FUyIsImhhbmRsZXIiLCJyZXEiLCJyZXMiLCJzZXNzaW9uIiwidXNlciIsInN0YXR1cyIsImpzb24iLCJlcnJvciIsImlzQWRtaW4iLCJndWlsZElkIiwidG9rZW4iLCJib3QiLCJjb25zb2xlIiwibWV0aG9kIiwiZGV0YWlscyIsImxvZyIsInF1ZXJ5IiwiaGVhZGVycyIsInRva2VuUHJvdmlkZWQiLCJmZXRjaE9wdGlvbnMiLCJ1cmwiLCJBdXRob3JpemF0aW9uIiwicGVybWlzc2lvbnNDaGVja1Jlc3BvbnNlIiwiZmV0Y2giLCJvayIsInBlcm1pc3Npb25zRXJyb3JCb2R5IiwidGV4dCIsInN0YXR1c1RleHQiLCJib2R5IiwicmVzcG9uc2UiLCJPYmplY3QiLCJmcm9tRW50cmllcyIsImVudHJpZXMiLCJlcnJvckJvZHkiLCJwYXJzZUVycm9yIiwiY2hhbm5lbHMiLCJsZW5ndGgiLCJmb3JtYXR0ZWRDaGFubmVscyIsIm1hcCIsImNoYW5uZWwiLCJmb3JtYXR0ZWRDaGFubmVsIiwiaWQiLCJuYW1lIiwidHlwZSIsInBvc2l0aW9uIiwicGFyZW50X2lkIiwicmF3X3R5cGUiLCJ0b3BpYyIsIm5zZnciLCJyYXRlX2xpbWl0X3Blcl91c2VyIiwiYml0cmF0ZSIsInVzZXJfbGltaXQiLCJtZXNzYWdlIiwic3RhY2siLCJ1c2VyTGltaXQiLCJwYXJlbnQiLCJyYXRlTGltaXRQZXJVc2VyIiwibnVtZXJpY1R5cGUiLCJvcmlnaW5hbCIsImNvbnZlcnRlZCIsImNoYW5uZWxEYXRhIiwiQm9vbGVhbiIsImlzTmFOIiwiSlNPTiIsInN0cmluZ2lmeSIsIm5ld0NoYW5uZWwiLCJjaGFubmVsSWQiLCJ1cGRhdGVkQ2hhbm5lbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/channels.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
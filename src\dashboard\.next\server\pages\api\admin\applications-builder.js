"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/applications-builder";
exports.ids = ["pages/api/admin/applications-builder"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\applications-builder\\index.ts */ \"(api-node)/./pages/api/admin/applications-builder/index.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/applications-builder\",\n        pathname: \"/api/admin/applications-builder\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./apiHelpers/db.ts":
/*!**************************!*\
  !*** ./apiHelpers/db.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDb: () => (/* binding */ getDb)\n/* harmony export */ });\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/config */ \"(api-node)/./core/config.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_1__);\n// Simple Mongo helper for API routes\n\n\nlet cachedClient = null;\nlet cachedDb = null;\nasync function getDb() {\n    if (cachedDb && cachedClient) {\n        // Check if connection is still alive\n        try {\n            await cachedDb.admin().ping();\n            return cachedDb;\n        } catch (error) {\n            // Connection is dead, reset cache\n            cachedClient = null;\n            cachedDb = null;\n        }\n    }\n    const mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\n    const dbName = _core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.name || 'discord_bot';\n    if (!cachedClient) {\n        // Use optimized connection options to reduce connection creation\n        const options = {\n            maxPoolSize: 10,\n            minPoolSize: 2,\n            maxIdleTimeMS: 30000,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            connectTimeoutMS: 10000,\n            retryWrites: true,\n            retryReads: true,\n            // Reduce connection logging\n            monitorCommands: false,\n            ..._core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.options\n        };\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_1__.MongoClient.connect(mongoUrl, options);\n    }\n    cachedDb = cachedClient.db(dbName);\n    return cachedDb;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./apiHelpers/db.ts\n");

/***/ }),

/***/ "(api-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./core/config.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/applications-builder/index.ts":
/*!*******************************************************!*\
  !*** ./pages/api/admin/applications-builder/index.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _apiHelpers_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../apiHelpers/db */ \"(api-node)/./apiHelpers/db.ts\");\n\n\n\nasync function handler(req, res) {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.user?.id) {\n        return res.status(401).json({\n            error: 'Unauthorized'\n        });\n    }\n    // Check if user is admin\n    const isAdmin = session.user?.isAdmin;\n    if (!isAdmin) {\n        return res.status(403).json({\n            error: 'Admin access required'\n        });\n    }\n    const db = await (0,_apiHelpers_db__WEBPACK_IMPORTED_MODULE_2__.getDb)();\n    const collection = db.collection('custom_applications');\n    if (req.method === 'GET') {\n        try {\n            const applications = await collection.find({}).toArray();\n            // Remove MongoDB _id field from response to avoid update conflicts\n            const cleanApplications = applications.map(({ _id, ...app })=>app);\n            return res.status(200).json({\n                applications: cleanApplications\n            });\n        } catch (error) {\n            console.error('Error fetching applications:', error);\n            return res.status(500).json({\n                error: 'Failed to fetch applications'\n            });\n        }\n    }\n    if (req.method === 'POST') {\n        try {\n            const applicationData = req.body;\n            // Validate required fields\n            if (!applicationData.title || !applicationData.description) {\n                return res.status(400).json({\n                    error: 'Title and description are required'\n                });\n            }\n            if (!applicationData.id) {\n                return res.status(400).json({\n                    error: 'Application ID is required'\n                });\n            }\n            // Check if application ID already exists\n            const existingApp = await collection.findOne({\n                id: applicationData.id\n            });\n            if (existingApp) {\n                // Update existing application - exclude _id field\n                const updatedApp = {\n                    id: applicationData.id,\n                    title: applicationData.title,\n                    description: applicationData.description,\n                    color: applicationData.color,\n                    icon: applicationData.icon,\n                    enabled: applicationData.enabled,\n                    questions: applicationData.questions,\n                    settings: applicationData.settings,\n                    updatedAt: new Date()\n                };\n                const result = await collection.updateOne({\n                    id: applicationData.id\n                }, {\n                    $set: updatedApp\n                });\n                return res.status(200).json({\n                    application: updatedApp\n                });\n            } else {\n                // Create new application\n                const newApp = {\n                    ...applicationData,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: session.user.id\n                };\n                const result = await collection.insertOne(newApp);\n                return res.status(201).json({\n                    application: newApp\n                });\n            }\n        } catch (error) {\n            console.error('Error saving application:', error);\n            return res.status(500).json({\n                error: 'Failed to save application'\n            });\n        }\n    }\n    return res.status(405).json({\n        error: 'Method not allowed'\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL3BhZ2VzL2FwaS9hZG1pbi9hcHBsaWNhdGlvbnMtYnVpbGRlci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNrRDtBQUNLO0FBQ0w7QUEwQ25DLGVBQWVHLFFBQVFDLEdBQW1CLEVBQUVDLEdBQW9CO0lBQzdFLE1BQU1DLFVBQVUsTUFBTU4sZ0VBQWdCQSxDQUFDSSxLQUFLQyxLQUFLSix3REFBV0E7SUFDNUQsSUFBSSxDQUFDSyxTQUFTQyxNQUFNQyxJQUFJO1FBQ3RCLE9BQU9ILElBQUlJLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFBRUMsT0FBTztRQUFlO0lBQ3REO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1DLFVBQVdOLFFBQVFDLElBQUksRUFBVUs7SUFDdkMsSUFBSSxDQUFDQSxTQUFTO1FBQ1osT0FBT1AsSUFBSUksTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXdCO0lBQy9EO0lBRUEsTUFBTUUsS0FBSyxNQUFNWCxxREFBS0E7SUFDdEIsTUFBTVksYUFBYUQsR0FBR0MsVUFBVSxDQUFDO0lBRWpDLElBQUlWLElBQUlXLE1BQU0sS0FBSyxPQUFPO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxlQUFlLE1BQU1GLFdBQVdHLElBQUksQ0FBQyxDQUFDLEdBQUdDLE9BQU87WUFDdEQsbUVBQW1FO1lBQ25FLE1BQU1DLG9CQUFvQkgsYUFBYUksR0FBRyxDQUFDLENBQUMsRUFBRUMsR0FBRyxFQUFFLEdBQUdDLEtBQUssR0FBS0E7WUFDaEUsT0FBT2pCLElBQUlJLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQUVNLGNBQWNHO1lBQWtCO1FBQ2hFLEVBQUUsT0FBT1IsT0FBTztZQUNkWSxRQUFRWixLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQStCO1FBQ3RFO0lBQ0Y7SUFFQSxJQUFJUCxJQUFJVyxNQUFNLEtBQUssUUFBUTtRQUN6QixJQUFJO1lBQ0YsTUFBTVMsa0JBQWtCcEIsSUFBSXFCLElBQUk7WUFFaEMsMkJBQTJCO1lBQzNCLElBQUksQ0FBQ0QsZ0JBQWdCRSxLQUFLLElBQUksQ0FBQ0YsZ0JBQWdCRyxXQUFXLEVBQUU7Z0JBQzFELE9BQU90QixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUFxQztZQUM1RTtZQUVBLElBQUksQ0FBQ2EsZ0JBQWdCaEIsRUFBRSxFQUFFO2dCQUN2QixPQUFPSCxJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUE2QjtZQUNwRTtZQUVBLHlDQUF5QztZQUN6QyxNQUFNaUIsY0FBYyxNQUFNZCxXQUFXZSxPQUFPLENBQUM7Z0JBQUVyQixJQUFJZ0IsZ0JBQWdCaEIsRUFBRTtZQUFDO1lBRXRFLElBQUlvQixhQUFhO2dCQUNmLGtEQUFrRDtnQkFDbEQsTUFBTUUsYUFBYTtvQkFDakJ0QixJQUFJZ0IsZ0JBQWdCaEIsRUFBRTtvQkFDdEJrQixPQUFPRixnQkFBZ0JFLEtBQUs7b0JBQzVCQyxhQUFhSCxnQkFBZ0JHLFdBQVc7b0JBQ3hDSSxPQUFPUCxnQkFBZ0JPLEtBQUs7b0JBQzVCQyxNQUFNUixnQkFBZ0JRLElBQUk7b0JBQzFCQyxTQUFTVCxnQkFBZ0JTLE9BQU87b0JBQ2hDQyxXQUFXVixnQkFBZ0JVLFNBQVM7b0JBQ3BDQyxVQUFVWCxnQkFBZ0JXLFFBQVE7b0JBQ2xDQyxXQUFXLElBQUlDO2dCQUNqQjtnQkFFQSxNQUFNQyxTQUFTLE1BQU14QixXQUFXeUIsU0FBUyxDQUN2QztvQkFBRS9CLElBQUlnQixnQkFBZ0JoQixFQUFFO2dCQUFDLEdBQ3pCO29CQUFFZ0MsTUFBTVY7Z0JBQVc7Z0JBR3JCLE9BQU96QixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFK0IsYUFBYVg7Z0JBQVc7WUFDeEQsT0FBTztnQkFDTCx5QkFBeUI7Z0JBQ3pCLE1BQU1ZLFNBQVM7b0JBQ2IsR0FBR2xCLGVBQWU7b0JBQ2xCbUIsV0FBVyxJQUFJTjtvQkFDZkQsV0FBVyxJQUFJQztvQkFDZk8sV0FBV3RDLFFBQVFDLElBQUksQ0FBQ0MsRUFBRTtnQkFDNUI7Z0JBRUEsTUFBTThCLFNBQVMsTUFBTXhCLFdBQVcrQixTQUFTLENBQUNIO2dCQUMxQyxPQUFPckMsSUFBSUksTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztvQkFBRStCLGFBQWFDO2dCQUFPO1lBQ3BEO1FBQ0YsRUFBRSxPQUFPL0IsT0FBTztZQUNkWSxRQUFRWixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQTZCO1FBQ3BFO0lBQ0Y7SUFFQSxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1FBQUVDLE9BQU87SUFBcUI7QUFDNUQiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXHNyY1xcZGFzaGJvYXJkXFxwYWdlc1xcYXBpXFxhZG1pblxcYXBwbGljYXRpb25zLWJ1aWxkZXJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xyXG5pbXBvcnQgeyBnZXRTZXJ2ZXJTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL25leHQnO1xyXG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJy4uLy4uL2F1dGgvWy4uLm5leHRhdXRoXSc7XHJcbmltcG9ydCB7IGdldERiIH0gZnJvbSAnLi4vLi4vLi4vLi4vYXBpSGVscGVycy9kYic7XHJcblxyXG5pbnRlcmZhY2UgQXBwbGljYXRpb25UeXBlIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBjb2xvcjogc3RyaW5nO1xyXG4gIGljb246IHN0cmluZztcclxuICBlbmFibGVkOiBib29sZWFuO1xyXG4gIHF1ZXN0aW9uczogUXVlc3Rpb25bXTtcclxuICBzZXR0aW5nczogQXBwbGljYXRpb25TZXR0aW5ncztcclxuICBjcmVhdGVkQXQ6IERhdGU7XHJcbiAgdXBkYXRlZEF0OiBEYXRlO1xyXG4gIGNyZWF0ZWRCeTogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUXVlc3Rpb24ge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdHlwZTogJ3RleHQnIHwgJ3RleHRhcmVhJyB8ICdzZWxlY3QnIHwgJ3JhZGlvJyB8ICdjaGVja2JveCcgfCAnbnVtYmVyJyB8ICdlbWFpbCc7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBwbGFjZWhvbGRlcj86IHN0cmluZztcclxuICByZXF1aXJlZDogYm9vbGVhbjtcclxuICBvcHRpb25zPzogc3RyaW5nW107XHJcbiAgdmFsaWRhdGlvbj86IHtcclxuICAgIG1pbkxlbmd0aD86IG51bWJlcjtcclxuICAgIG1heExlbmd0aD86IG51bWJlcjtcclxuICAgIHBhdHRlcm4/OiBzdHJpbmc7XHJcbiAgfTtcclxufVxyXG5cclxuaW50ZXJmYWNlIEFwcGxpY2F0aW9uU2V0dGluZ3Mge1xyXG4gIGFsbG93TXVsdGlwbGVTdWJtaXNzaW9uczogYm9vbGVhbjtcclxuICByZXF1aXJlQXBwcm92YWw6IGJvb2xlYW47XHJcbiAgYXV0b1Jlc3BvbnNlOiBib29sZWFuO1xyXG4gIG5vdGlmaWNhdGlvbkNoYW5uZWxzOiBzdHJpbmdbXTtcclxuICBvcGVuaW5nU2NoZWR1bGU/OiB7XHJcbiAgICBlbmFibGVkOiBib29sZWFuO1xyXG4gICAgc3RhcnREYXRlOiBzdHJpbmc7XHJcbiAgICBlbmREYXRlOiBzdHJpbmc7XHJcbiAgfTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXE6IE5leHRBcGlSZXF1ZXN0LCByZXM6IE5leHRBcGlSZXNwb25zZSkge1xyXG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKHJlcSwgcmVzLCBhdXRoT3B0aW9ucyk7XHJcbiAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xyXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAxKS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhZG1pblxyXG4gIGNvbnN0IGlzQWRtaW4gPSAoc2Vzc2lvbi51c2VyIGFzIGFueSk/LmlzQWRtaW47XHJcbiAgaWYgKCFpc0FkbWluKSB7XHJcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDMpLmpzb24oeyBlcnJvcjogJ0FkbWluIGFjY2VzcyByZXF1aXJlZCcgfSk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBkYiA9IGF3YWl0IGdldERiKCk7XHJcbiAgY29uc3QgY29sbGVjdGlvbiA9IGRiLmNvbGxlY3Rpb24oJ2N1c3RvbV9hcHBsaWNhdGlvbnMnKTtcclxuXHJcbiAgaWYgKHJlcS5tZXRob2QgPT09ICdHRVQnKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBhcHBsaWNhdGlvbnMgPSBhd2FpdCBjb2xsZWN0aW9uLmZpbmQoe30pLnRvQXJyYXkoKTtcclxuICAgICAgLy8gUmVtb3ZlIE1vbmdvREIgX2lkIGZpZWxkIGZyb20gcmVzcG9uc2UgdG8gYXZvaWQgdXBkYXRlIGNvbmZsaWN0c1xyXG4gICAgICBjb25zdCBjbGVhbkFwcGxpY2F0aW9ucyA9IGFwcGxpY2F0aW9ucy5tYXAoKHsgX2lkLCAuLi5hcHAgfSkgPT4gYXBwKTtcclxuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHsgYXBwbGljYXRpb25zOiBjbGVhbkFwcGxpY2F0aW9ucyB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGFwcGxpY2F0aW9uczonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDUwMCkuanNvbih7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGFwcGxpY2F0aW9ucycgfSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBpZiAocmVxLm1ldGhvZCA9PT0gJ1BPU1QnKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBhcHBsaWNhdGlvbkRhdGEgPSByZXEuYm9keSBhcyBBcHBsaWNhdGlvblR5cGU7XHJcbiAgICAgIFxyXG4gICAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcclxuICAgICAgaWYgKCFhcHBsaWNhdGlvbkRhdGEudGl0bGUgfHwgIWFwcGxpY2F0aW9uRGF0YS5kZXNjcmlwdGlvbikge1xyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IGVycm9yOiAnVGl0bGUgYW5kIGRlc2NyaXB0aW9uIGFyZSByZXF1aXJlZCcgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghYXBwbGljYXRpb25EYXRhLmlkKSB7XHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAwKS5qc29uKHsgZXJyb3I6ICdBcHBsaWNhdGlvbiBJRCBpcyByZXF1aXJlZCcgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGFwcGxpY2F0aW9uIElEIGFscmVhZHkgZXhpc3RzXHJcbiAgICAgIGNvbnN0IGV4aXN0aW5nQXBwID0gYXdhaXQgY29sbGVjdGlvbi5maW5kT25lKHsgaWQ6IGFwcGxpY2F0aW9uRGF0YS5pZCB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChleGlzdGluZ0FwcCkge1xyXG4gICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBhcHBsaWNhdGlvbiAtIGV4Y2x1ZGUgX2lkIGZpZWxkXHJcbiAgICAgICAgY29uc3QgdXBkYXRlZEFwcCA9IHtcclxuICAgICAgICAgIGlkOiBhcHBsaWNhdGlvbkRhdGEuaWQsXHJcbiAgICAgICAgICB0aXRsZTogYXBwbGljYXRpb25EYXRhLnRpdGxlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGFwcGxpY2F0aW9uRGF0YS5kZXNjcmlwdGlvbixcclxuICAgICAgICAgIGNvbG9yOiBhcHBsaWNhdGlvbkRhdGEuY29sb3IsXHJcbiAgICAgICAgICBpY29uOiBhcHBsaWNhdGlvbkRhdGEuaWNvbixcclxuICAgICAgICAgIGVuYWJsZWQ6IGFwcGxpY2F0aW9uRGF0YS5lbmFibGVkLFxyXG4gICAgICAgICAgcXVlc3Rpb25zOiBhcHBsaWNhdGlvbkRhdGEucXVlc3Rpb25zLFxyXG4gICAgICAgICAgc2V0dGluZ3M6IGFwcGxpY2F0aW9uRGF0YS5zZXR0aW5ncyxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICB9O1xyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbGxlY3Rpb24udXBkYXRlT25lKFxyXG4gICAgICAgICAgeyBpZDogYXBwbGljYXRpb25EYXRhLmlkIH0sXHJcbiAgICAgICAgICB7ICRzZXQ6IHVwZGF0ZWRBcHAgfVxyXG4gICAgICAgICk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHsgYXBwbGljYXRpb246IHVwZGF0ZWRBcHAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBhcHBsaWNhdGlvblxyXG4gICAgICAgIGNvbnN0IG5ld0FwcCA9IHtcclxuICAgICAgICAgIC4uLmFwcGxpY2F0aW9uRGF0YSxcclxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIGNyZWF0ZWRCeTogc2Vzc2lvbi51c2VyLmlkLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29sbGVjdGlvbi5pbnNlcnRPbmUobmV3QXBwKTtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cygyMDEpLmpzb24oeyBhcHBsaWNhdGlvbjogbmV3QXBwIH0pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgYXBwbGljYXRpb246JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBzYXZlIGFwcGxpY2F0aW9uJyB9KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiByZXMuc3RhdHVzKDQwNSkuanNvbih7IGVycm9yOiAnTWV0aG9kIG5vdCBhbGxvd2VkJyB9KTtcclxufSAiXSwibmFtZXMiOlsiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwiZ2V0RGIiLCJoYW5kbGVyIiwicmVxIiwicmVzIiwic2Vzc2lvbiIsInVzZXIiLCJpZCIsInN0YXR1cyIsImpzb24iLCJlcnJvciIsImlzQWRtaW4iLCJkYiIsImNvbGxlY3Rpb24iLCJtZXRob2QiLCJhcHBsaWNhdGlvbnMiLCJmaW5kIiwidG9BcnJheSIsImNsZWFuQXBwbGljYXRpb25zIiwibWFwIiwiX2lkIiwiYXBwIiwiY29uc29sZSIsImFwcGxpY2F0aW9uRGF0YSIsImJvZHkiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZXhpc3RpbmdBcHAiLCJmaW5kT25lIiwidXBkYXRlZEFwcCIsImNvbG9yIiwiaWNvbiIsImVuYWJsZWQiLCJxdWVzdGlvbnMiLCJzZXR0aW5ncyIsInVwZGF0ZWRBdCIsIkRhdGUiLCJyZXN1bHQiLCJ1cGRhdGVPbmUiLCIkc2V0IiwiYXBwbGljYXRpb24iLCJuZXdBcHAiLCJjcmVhdGVkQXQiLCJjcmVhdGVkQnkiLCJpbnNlcnRPbmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/applications-builder/index.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
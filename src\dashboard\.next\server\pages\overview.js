"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/overview";
exports.ids = ["pages/overview"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Coverview.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Coverview.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\overview.tsx */ \"(pages-dir-node)/./pages/overview.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/overview\",\n        pathname: \"/overview\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_overview_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Coverview.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(pages-dir-node)/./components/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(pages-dir-node)/./components/Sidebar.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        minH: \"100vh\",\n        bg: currentScheme.colors.background,\n        position: \"relative\",\n        overflow: \"hidden\",\n        _before: {\n            content: '\"\"',\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bgImage: `\n          radial-gradient(circle at 15% 50%, ${currentScheme.colors.primary}15 0%, transparent 25%),\n          radial-gradient(circle at 85% 30%, ${currentScheme.colors.accent}15 0%, transparent 25%)\n        `,\n            zIndex: 0\n        },\n        _after: {\n            content: '\"\"',\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backdropFilter: 'blur(100px)',\n            zIndex: 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n            position: \"relative\",\n            zIndex: 1,\n            display: \"flex\",\n            flexDirection: \"column\",\n            minH: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 30,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    display: \"flex\",\n                    flex: \"1\",\n                    position: \"relative\",\n                    pt: \"4rem\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                            position: \"fixed\",\n                            top: \"4rem\",\n                            bottom: 0,\n                            left: 0,\n                            w: \"64\",\n                            zIndex: 20,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                            flex: \"1\",\n                            ml: \"64\",\n                            p: {\n                                base: 4,\n                                md: 8\n                            },\n                            maxW: \"100%\",\n                            transition: \"all 0.3s\",\n                            position: \"relative\",\n                            _before: {\n                                content: '\"\"',\n                                position: 'absolute',\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                bottom: 0,\n                                bg: 'linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)',\n                                pointerEvents: 'none',\n                                zIndex: -1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                                maxW: \"container.xl\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiLogOut_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiLogOut!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationCenter */ \"(pages-dir-node)/./components/NotificationCenter.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__, _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__, _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\nfunction Navbar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const headingText = displayName ? `${displayName} Dashboard` : 'Bot Dashboard';\n    // Show experimental announcement if applications are open and user is not a developer\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        px: 6,\n        py: 2,\n        bg: \"rgba(255,255,255,0.05)\",\n        backdropFilter: \"blur(20px)\",\n        borderBottom: \"1px solid\",\n        borderColor: \"whiteAlpha.200\",\n        position: \"sticky\",\n        top: 0,\n        zIndex: 1000,\n        _before: {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bg: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))',\n            zIndex: -1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {\n            h: 16,\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    flex: \"1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                        as: \"h1\",\n                        fontSize: \"xl\",\n                        bgGradient: \"linear(to-r, blue.400, purple.400)\",\n                        bgClip: \"text\",\n                        _hover: {\n                            bgGradient: \"linear(to-r, blue.300, purple.300)\",\n                            transform: \"scale(1.02)\"\n                        },\n                        transition: \"all 0.2s\",\n                        children: headingText\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    flex: \"1\",\n                    display: \"flex\",\n                    justifyContent: \"flex-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {\n                        alignItems: \"center\",\n                        gap: 4,\n                        children: session?.user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                            spacing: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationCenter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Menu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuButton, {\n                                            as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button,\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            px: 2,\n                                            py: 1,\n                                            borderRadius: \"full\",\n                                            _hover: {\n                                                bg: \"whiteAlpha.200\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                spacing: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                                        size: \"sm\",\n                                                        name: session.user.name ?? undefined,\n                                                        src: session.user.image ?? undefined,\n                                                        borderWidth: 2,\n                                                        borderColor: \"blue.400\",\n                                                        _hover: {\n                                                            borderColor: \"purple.400\",\n                                                            transform: \"scale(1.05)\"\n                                                        },\n                                                        transition: \"all 0.2s\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                        color: \"gray.300\",\n                                                        display: {\n                                                            base: \"none\",\n                                                            md: \"block\"\n                                                        },\n                                                        children: session.user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuList, {\n                                            bg: \"gray.800\",\n                                            borderColor: \"whiteAlpha.200\",\n                                            boxShadow: \"lg\",\n                                            _hover: {\n                                                borderColor: \"blue.400\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuItem, {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLogOut, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\",\n                                                    color: \"red.400\"\n                                                },\n                                                children: \"Sign out\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)('discord', {\n                                    callbackUrl: '/overview'\n                                }),\n                            bgGradient: \"linear(to-r, blue.500, purple.500)\",\n                            color: \"white\",\n                            _hover: {\n                                bgGradient: \"linear(to-r, blue.400, purple.400)\",\n                                transform: \"translateY(-1px)\"\n                            },\n                            _active: {\n                                bgGradient: \"linear(to-r, blue.600, purple.600)\",\n                                transform: \"translateY(1px)\"\n                            },\n                            transition: \"all 0.2s\",\n                            children: \"Login with Discord\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/NotificationCenter.tsx":
/*!*******************************************!*\
  !*** ./components/NotificationCenter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _barrel_optimize_names_FiBell_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBell!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n// Lightweight notification center - minimal functionality to reduce bundle size\nfunction NotificationCenter() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [notifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]); // Empty for now to reduce complexity\n    // Don't render if no session\n    if (!session?.user) {\n        return null;\n    }\n    const unreadCount = 0; // Simplified for now\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n        placement: \"bottom-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    position: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            label: \"Notifications\",\n                            placement: \"bottom\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                \"aria-label\": \"Notifications\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBell_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBell, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                color: \"gray.300\",\n                                _hover: {\n                                    bg: \"whiteAlpha.200\",\n                                    color: \"white\",\n                                    transform: \"scale(1.05)\"\n                                },\n                                transition: \"all 0.2s\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            position: \"absolute\",\n                            top: \"-1\",\n                            right: \"-1\",\n                            colorScheme: \"red\",\n                            borderRadius: \"full\",\n                            fontSize: \"xs\",\n                            minW: \"18px\",\n                            h: \"18px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: unreadCount > 99 ? '99+' : unreadCount\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                bg: \"gray.800\",\n                borderColor: \"whiteAlpha.200\",\n                boxShadow: \"2xl\",\n                maxW: \"400px\",\n                _focus: {\n                    boxShadow: \"2xl\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverHeader, {\n                        borderBottomColor: \"whiteAlpha.200\",\n                        fontWeight: \"semibold\",\n                        fontSize: \"lg\",\n                        color: \"white\",\n                        children: \"Notifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverBody, {\n                        maxH: \"400px\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                            spacing: 0,\n                            align: \"stretch\",\n                            children: !notifications || notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                py: 8,\n                                textAlign: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    color: \"gray.400\",\n                                    fontSize: \"sm\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this) : (notifications || []).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                    p: 3,\n                                    borderBottom: \"1px\",\n                                    borderColor: \"whiteAlpha.100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"white\",\n                                            fontWeight: \"medium\",\n                                            children: notification.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            fontSize: \"xs\",\n                                            color: \"gray.400\",\n                                            mt: 1,\n                                            children: notification.message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, notification.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/NotificationCenter.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/OverviewCard.tsx":
/*!*************************************!*\
  !*** ./components/OverviewCard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverviewCard: () => (/* binding */ OverviewCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst OverviewCard = ({ title, description, icon, href, color, gradient, accentColor, disabled = false, experimental = false })=>{\n    const isClickable = href && href !== '#' && !disabled;\n    const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        px: 10,\n        py: 5,\n        bg: gradient ? `linear-gradient(135deg, ${gradient.from}, ${gradient.to})` : \"gray.800\",\n        borderRadius: \"lg\",\n        border: \"1px solid\",\n        borderColor: disabled ? \"whiteAlpha.100\" : \"whiteAlpha.200\",\n        transition: \"all 0.3s\",\n        h: \"140px\",\n        minW: \"360px\",\n        w: \"full\",\n        overflow: \"hidden\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        cursor: isClickable ? \"pointer\" : disabled ? \"not-allowed\" : \"default\",\n        position: \"relative\",\n        opacity: disabled ? 0.6 : 1,\n        _before: {\n            content: '\"\"',\n            position: \"absolute\",\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bg: experimental ? \"repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)\" : \"none\",\n            opacity: 0.5,\n            pointerEvents: \"none\"\n        },\n        _hover: isClickable ? {\n            transform: 'translateY(-3px)',\n            boxShadow: `0 6px 14px ${accentColor || `var(--chakra-colors-${color}-900)`}40`,\n            borderColor: `${color}.400`,\n            _before: {\n                opacity: 0.7\n            }\n        } : {},\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n            spacing: 4,\n            align: \"start\",\n            flex: \"1\",\n            justify: \"flex-start\",\n            h: \"full\",\n            position: \"relative\",\n            zIndex: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                    spacing: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                            as: icon,\n                            boxSize: 6,\n                            color: accentColor || `${color}.300`,\n                            filter: experimental ? \"drop-shadow(0 0 2px currentColor)\" : \"none\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {\n                            size: \"md\",\n                            color: \"white\",\n                            noOfLines: 1,\n                            whiteSpace: \"nowrap\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                    color: disabled ? \"gray.500\" : \"gray.300\",\n                    fontSize: \"sm\",\n                    lineHeight: \"1.4\",\n                    noOfLines: 3,\n                    overflow: \"hidden\",\n                    textOverflow: \"ellipsis\",\n                    flex: \"1\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n    if (isClickable) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n            href: href,\n            passHref: true,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\OverviewCard.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, undefined);\n    }\n    return cardContent;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/OverviewCard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Import package.json version\nconst BOT_VERSION = '1.0.0'; // You can update this manually or import from package.json\nconst DEVELOPER_ID = '933023999770918932';\nfunction Sidebar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const isAdmin = session?.user?.isAdmin;\n    const userId = session?.user?.id;\n    const [isAdminExpanded, setIsAdminExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isApplicationsExpanded, setIsApplicationsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [applicationsOpen, setApplicationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [openApplications, setOpenApplications] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const fetchApplicationStatus = {\n                \"Sidebar.useEffect.fetchApplicationStatus\": async ()=>{\n                    try {\n                        console.group('Sidebar Fetch Diagnostics');\n                        console.log('Starting application status fetch...');\n                        // Fetch application config\n                        const configRes = await fetch('/api/applications/config', {\n                            credentials: 'include'\n                        });\n                        if (!configRes.ok) {\n                            console.warn('Failed to fetch application config:', configRes.status);\n                            setApplicationsOpen(false);\n                            setOpenApplications([]);\n                            return;\n                        }\n                        const configData = await configRes.json();\n                        setApplicationsOpen(configData.isOpen || configData.open);\n                        // Fetch applications only if config indicates they're open\n                        if (configData.isOpen || configData.open) {\n                            try {\n                                const appsRes = await fetch('/api/admin/applications-builder', {\n                                    credentials: 'include'\n                                });\n                                if (!appsRes.ok) {\n                                    console.warn('Failed to fetch applications:', appsRes.status);\n                                    setOpenApplications([]);\n                                    return;\n                                }\n                                const appsData = await appsRes.json();\n                                setOpenApplications(appsData);\n                            } catch (appsError) {\n                                console.error('Error fetching applications:', appsError);\n                                setOpenApplications([]);\n                            }\n                        }\n                        // Attempt to fetch Discord channels with error handling\n                        try {\n                            const channelsRes = await fetch('/api/discord/channels', {\n                                credentials: 'include'\n                            });\n                            if (!channelsRes.ok) {\n                                console.warn('Failed to fetch Discord channels:', {\n                                    status: channelsRes.status,\n                                    statusText: channelsRes.statusText\n                                });\n                                // Try to get error details\n                                const errorDetails = await channelsRes.json().catch({\n                                    \"Sidebar.useEffect.fetchApplicationStatus\": ()=>({})\n                                }[\"Sidebar.useEffect.fetchApplicationStatus\"]);\n                                console.warn('Channel fetch error details:', errorDetails);\n                            }\n                        } catch (channelsError) {\n                            console.error('Unexpected error fetching Discord channels:', channelsError);\n                        }\n                        console.groupEnd();\n                    } catch (error) {\n                        console.error('Comprehensive fetch error:', error);\n                        setApplicationsOpen(false);\n                        setOpenApplications([]);\n                    }\n                }\n            }[\"Sidebar.useEffect.fetchApplicationStatus\"];\n            fetchApplicationStatus();\n        }\n    }[\"Sidebar.useEffect\"], []);\n    const menuItems = [\n        {\n            name: 'Overview',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHome,\n            href: '/overview'\n        },\n        {\n            name: 'Applications',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPackage,\n            href: '/applications'\n        },\n        {\n            name: 'Tickets',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHelpCircle,\n            href: '/tickets'\n        },\n        {\n            name: 'Game Servers',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMonitor,\n            href: '/gameservers'\n        }\n    ];\n    // Admin functionality is now handled through the expandable admin section below\n    const adminQuickLinks = [\n        {\n            name: 'Server Management',\n            href: '/admin/guilds',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings\n        },\n        {\n            name: 'Addons',\n            href: '/admin/addons',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBox\n        },\n        {\n            name: 'Errors',\n            href: '/admin/errors',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertCircle\n        }\n    ];\n    // All experimental features are now consolidated into the main Applications page\n    // Experimental admin links for developers (Removed)\n    const isActive = (href)=>{\n        if (href === '/overview') {\n            return router.pathname === href;\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        as: \"nav\",\n        h: \"100%\",\n        bg: currentScheme.colors.surface,\n        backdropFilter: \"blur(20px)\",\n        borderRight: \"1px solid\",\n        borderColor: currentScheme.colors.border,\n        py: 8,\n        display: \"flex\",\n        flexDirection: \"column\",\n        _before: {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bg: `linear-gradient(180deg, ${currentScheme.colors.primary}15 0%, ${currentScheme.colors.accent}15 100%)`,\n            zIndex: -1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                spacing: 2,\n                align: \"stretch\",\n                flex: \"1\",\n                children: [\n                    menuItems.map((item)=>{\n                        const active = isActive(item.href);\n                        if (item.name === 'Applications') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                spacing: 0,\n                                align: \"stretch\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        px: 4,\n                                        py: 3,\n                                        fontSize: \"sm\",\n                                        fontWeight: \"medium\",\n                                        color: isApplicationsExpanded || isActive(item.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                        bg: isActive(item.href) ? `${currentScheme.colors.primary}30` : 'transparent',\n                                        _hover: {\n                                            bg: isActive(item.href) ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,\n                                            color: currentScheme.colors.text,\n                                            transform: 'translateX(4px)'\n                                        },\n                                        _active: {\n                                            bg: `${currentScheme.colors.primary}50`\n                                        },\n                                        borderRight: isActive(item.href) ? '2px solid' : 'none',\n                                        borderColor: isActive(item.href) ? currentScheme.colors.primary : 'transparent',\n                                        transition: \"all 0.2s\",\n                                        cursor: \"pointer\",\n                                        role: \"group\",\n                                        onClick: ()=>setIsApplicationsExpanded(!isApplicationsExpanded),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                as: item.icon,\n                                                w: 5,\n                                                h: 5,\n                                                mr: 3,\n                                                transition: \"all 0.2s\",\n                                                _groupHover: {\n                                                    transform: 'scale(1.1)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                display: {\n                                                    base: 'none',\n                                                    lg: 'block'\n                                                },\n                                                bgGradient: isActive(item.href) ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none',\n                                                bgClip: isActive(item.href) ? 'text' : 'none',\n                                                transition: \"all 0.2s\",\n                                                flex: \"1\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiChevronDown,\n                                                w: 4,\n                                                h: 4,\n                                                ml: 2,\n                                                transition: \"all 0.2s\",\n                                                transform: isApplicationsExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n                                                opacity: 0.6\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Collapse, {\n                                        in: isApplicationsExpanded,\n                                        animateOpacity: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                            spacing: 1,\n                                            align: \"stretch\",\n                                            pl: 4,\n                                            py: 2,\n                                            children: openApplications.length > 0 ? openApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                    as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                    href: `/applications/${app.id}`,\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    px: 4,\n                                                    py: 2,\n                                                    fontSize: \"xs\",\n                                                    fontWeight: \"medium\",\n                                                    color: isActive(`/applications/${app.id}`) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                                    bg: isActive(`/applications/${app.id}`) ? `${currentScheme.colors.primary}20` : 'transparent',\n                                                    _hover: {\n                                                        bg: currentScheme.colors.surface,\n                                                        color: currentScheme.colors.text,\n                                                        transform: 'translateX(2px)'\n                                                    },\n                                                    borderRadius: \"md\",\n                                                    transition: \"all 0.2s\",\n                                                    role: \"group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                        display: {\n                                                            base: 'none',\n                                                            lg: 'block'\n                                                        },\n                                                        children: app.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, app.id, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                px: 4,\n                                                py: 2,\n                                                fontSize: \"xs\",\n                                                color: currentScheme.colors.textSecondary,\n                                                display: {\n                                                    base: 'none',\n                                                    lg: 'block'\n                                                },\n                                                children: \"No open applications\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            label: item.name,\n                            placement: \"right\",\n                            hasArrow: true,\n                            gutter: 20,\n                            openDelay: 500,\n                            display: {\n                                base: 'block',\n                                '2xl': 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                href: item.href,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                px: 4,\n                                py: 3,\n                                fontSize: \"sm\",\n                                fontWeight: \"medium\",\n                                color: active ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                bg: active ? `${currentScheme.colors.primary}30` : 'transparent',\n                                _hover: {\n                                    bg: active ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,\n                                    color: currentScheme.colors.text,\n                                    transform: 'translateX(4px)'\n                                },\n                                _active: {\n                                    bg: `${currentScheme.colors.primary}50`\n                                },\n                                borderRight: active ? '2px solid' : 'none',\n                                borderColor: active ? currentScheme.colors.primary : 'transparent',\n                                transition: \"all 0.2s\",\n                                role: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: item.icon,\n                                        w: 5,\n                                        h: 5,\n                                        mr: 3,\n                                        transition: \"all 0.2s\",\n                                        _groupHover: {\n                                            transform: 'scale(1.1)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        bgGradient: active ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none',\n                                        bgClip: active ? 'text' : 'none',\n                                        transition: \"all 0.2s\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name === 'Applications' && !applicationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                        ml: 2,\n                                        colorScheme: \"red\",\n                                        variant: \"solid\",\n                                        fontSize: \"xs\",\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        children: \"Closed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.name === 'Applications' && openApplications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                        ml: 2,\n                                        colorScheme: \"green\",\n                                        variant: \"solid\",\n                                        fontSize: \"xs\",\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        children: [\n                                            openApplications.length,\n                                            \" Open\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                        spacing: 0,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                px: 4,\n                                py: 3,\n                                fontSize: \"sm\",\n                                fontWeight: \"medium\",\n                                color: currentScheme.colors.textSecondary,\n                                bg: \"transparent\",\n                                _hover: {\n                                    bg: currentScheme.colors.surface,\n                                    color: currentScheme.colors.text,\n                                    transform: 'translateX(4px)'\n                                },\n                                transition: \"all 0.2s\",\n                                cursor: \"pointer\",\n                                role: \"group\",\n                                onClick: ()=>setIsAdminExpanded(!isAdminExpanded),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiServer,\n                                        w: 5,\n                                        h: 5,\n                                        mr: 3,\n                                        transition: \"all 0.2s\",\n                                        _groupHover: {\n                                            transform: 'scale(1.1)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        transition: \"all 0.2s\",\n                                        flex: \"1\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiChevronDown,\n                                        w: 4,\n                                        h: 4,\n                                        ml: 2,\n                                        transition: \"all 0.2s\",\n                                        transform: isAdminExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n                                        opacity: 0.6\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Collapse, {\n                                in: isAdminExpanded,\n                                animateOpacity: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                    spacing: 1,\n                                    align: \"stretch\",\n                                    pl: 4,\n                                    py: 2,\n                                    children: adminQuickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                            href: link.href,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            px: 4,\n                                            py: 2,\n                                            fontSize: \"xs\",\n                                            fontWeight: \"medium\",\n                                            color: isActive(link.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                            bg: isActive(link.href) ? `${currentScheme.colors.primary}20` : 'transparent',\n                                            _hover: {\n                                                bg: currentScheme.colors.surface,\n                                                color: currentScheme.colors.text,\n                                                transform: 'translateX(2px)'\n                                            },\n                                            borderRadius: \"md\",\n                                            transition: \"all 0.2s\",\n                                            role: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                    as: link.icon,\n                                                    w: 4,\n                                                    h: 4,\n                                                    mr: 2,\n                                                    transition: \"all 0.2s\",\n                                                    _groupHover: {\n                                                        transform: 'scale(1.1)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                    display: {\n                                                        base: 'none',\n                                                        lg: 'block'\n                                                    },\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, link.href, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                px: 4,\n                pt: 4,\n                mt: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Divider, {\n                        borderColor: currentScheme.colors.border,\n                        mb: 4\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                        fontSize: \"xs\",\n                        color: currentScheme.colors.textSecondary,\n                        textAlign: \"center\",\n                        bgGradient: `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})`,\n                        bgClip: \"text\",\n                        opacity: 0.7,\n                        _hover: {\n                            opacity: 1,\n                            transform: \"scale(1.05)\"\n                        },\n                        transition: \"all 0.2s\",\n                        children: displayName ? `${displayName} v${BOT_VERSION}` : `Bot v${BOT_VERSION}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./config/cards.ts":
/*!*************************!*\
  !*** ./config/cards.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CARD_CONFIGS: () => (/* binding */ CARD_CONFIGS)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n\nconst CARD_CONFIGS = [\n    {\n        id: 'overview',\n        title: 'Overview',\n        description: 'View server statistics and general information.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiActivity,\n        href: '/overview',\n        color: 'blue',\n        gradient: {\n            from: 'rgba(49, 130, 206, 0.4)',\n            to: 'rgba(49, 130, 206, 0.1)'\n        },\n        accentColor: '#63B3ED'\n    },\n    {\n        id: 'gameservers',\n        title: 'Game Servers',\n        description: 'Manage and monitor your game servers. View status, add or edit server configurations.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiMonitor,\n        href: '/gameservers',\n        color: 'green',\n        gradient: {\n            from: 'rgba(72, 187, 120, 0.4)',\n            to: 'rgba(72, 187, 120, 0.1)'\n        },\n        accentColor: '#68D391'\n    },\n    {\n        id: 'applications',\n        title: 'Applications',\n        description: 'Review and manage guild applications. Process new members and handle requests.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiPackage,\n        href: '/applications',\n        color: 'purple',\n        gradient: {\n            from: 'rgba(159, 122, 234, 0.4)',\n            to: 'rgba(159, 122, 234, 0.1)'\n        },\n        accentColor: '#B794F4'\n    },\n    {\n        id: 'tickets',\n        title: 'Support Tickets',\n        description: 'Track and manage support tickets. Respond to user inquiries and resolve issues.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiHelpCircle,\n        href: '/tickets',\n        color: 'orange',\n        gradient: {\n            from: 'rgba(237, 137, 54, 0.4)',\n            to: 'rgba(237, 137, 54, 0.1)'\n        },\n        accentColor: '#F6AD55'\n    },\n    {\n        id: 'moderation',\n        title: 'Moderation',\n        description: 'Tools and features for server moderators.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiLock,\n        href: '/moderation',\n        color: 'teal',\n        gradient: {\n            from: 'rgba(49, 151, 149, 0.4)',\n            to: 'rgba(49, 151, 149, 0.1)'\n        },\n        accentColor: '#4FD1C5',\n        requiredRole: 'moderator'\n    },\n    {\n        id: 'experimental',\n        title: 'Experimental Features',\n        description: 'Try out new features that are still in development. These may not work as expected.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiSettings,\n        href: '#',\n        color: 'yellow',\n        gradient: {\n            from: 'rgba(236, 201, 75, 0.4)',\n            to: 'rgba(236, 201, 75, 0.1)'\n        },\n        accentColor: '#F6E05E',\n        experimental: true,\n        disabled: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./config/cards.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_SCHEMES: () => (/* binding */ COLOR_SCHEMES),\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _styles_theme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/theme */ \"(pages-dir-node)/./styles/theme.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst COLOR_SCHEMES = [\n    {\n        id: 'default',\n        name: 'Default Purple',\n        description: 'Classic purple and blue gradient theme',\n        colors: {\n            primary: '#8b5cf6',\n            primaryLight: '#a78bfa',\n            primaryDark: '#7c3aed',\n            secondary: '#5865F2',\n            accent: '#06b6d4',\n            background: '#1a202c',\n            surface: 'rgba(255,255,255,0.03)',\n            text: '#f7fafc',\n            textSecondary: '#a0aec0',\n            border: 'rgba(255,255,255,0.2)',\n            success: '#68d391',\n            warning: '#fbb6ce',\n            error: '#fc8181',\n            info: '#63b3ed'\n        }\n    },\n    {\n        id: 'ocean',\n        name: 'Ocean Blue',\n        description: 'Deep blue ocean-inspired theme',\n        colors: {\n            primary: '#0ea5e9',\n            primaryLight: '#38bdf8',\n            primaryDark: '#0284c7',\n            secondary: '#06b6d4',\n            accent: '#8b5cf6',\n            background: '#0f172a',\n            surface: 'rgba(59, 130, 246, 0.05)',\n            text: '#f1f5f9',\n            textSecondary: '#94a3b8',\n            border: 'rgba(59, 130, 246, 0.2)',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6'\n        }\n    },\n    {\n        id: 'forest',\n        name: 'Forest Green',\n        description: 'Nature-inspired green theme',\n        colors: {\n            primary: '#059669',\n            primaryLight: '#10b981',\n            primaryDark: '#047857',\n            secondary: '#065f46',\n            accent: '#8b5cf6',\n            background: '#0f1419',\n            surface: 'rgba(16, 185, 129, 0.05)',\n            text: '#f0fdf4',\n            textSecondary: '#86efac',\n            border: 'rgba(16, 185, 129, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'sunset',\n        name: 'Sunset Orange',\n        description: 'Warm sunset-inspired theme',\n        colors: {\n            primary: '#ea580c',\n            primaryLight: '#fb923c',\n            primaryDark: '#c2410c',\n            secondary: '#dc2626',\n            accent: '#8b5cf6',\n            background: '#1c1917',\n            surface: 'rgba(251, 146, 60, 0.05)',\n            text: '#fef7ed',\n            textSecondary: '#fdba74',\n            border: 'rgba(251, 146, 60, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'rose',\n        name: 'Rose Pink',\n        description: 'Elegant rose and pink theme',\n        colors: {\n            primary: '#e11d48',\n            primaryLight: '#f43f5e',\n            primaryDark: '#be123c',\n            secondary: '#ec4899',\n            accent: '#8b5cf6',\n            background: '#1f1720',\n            surface: 'rgba(244, 63, 94, 0.05)',\n            text: '#fdf2f8',\n            textSecondary: '#fda4af',\n            border: 'rgba(244, 63, 94, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'midnight',\n        name: 'Midnight Blue',\n        description: 'Dark midnight blue theme',\n        colors: {\n            primary: '#1e40af',\n            primaryLight: '#3b82f6',\n            primaryDark: '#1e3a8a',\n            secondary: '#4338ca',\n            accent: '#06b6d4',\n            background: '#0c0a1f',\n            surface: 'rgba(59, 130, 246, 0.05)',\n            text: '#f8fafc',\n            textSecondary: '#94a3b8',\n            border: 'rgba(59, 130, 246, 0.2)',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6'\n        }\n    }\n];\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [currentScheme, setCurrentScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(COLOR_SCHEMES[0]);\n    const [customSchemes, setCustomSchemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedSchemeId = localStorage.getItem('dashboard-color-scheme');\n            const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');\n            if (savedCustomSchemes) {\n                try {\n                    const parsedCustomSchemes = JSON.parse(savedCustomSchemes);\n                    setCustomSchemes(parsedCustomSchemes);\n                } catch (error) {\n                    console.error('Failed to parse custom schemes:', error);\n                }\n            }\n            if (savedSchemeId) {\n                // First check built-in schemes\n                const builtInScheme = COLOR_SCHEMES.find({\n                    \"ThemeProvider.useEffect.builtInScheme\": (s)=>s.id === savedSchemeId\n                }[\"ThemeProvider.useEffect.builtInScheme\"]);\n                if (builtInScheme) {\n                    setCurrentScheme(builtInScheme);\n                } else {\n                    // Check custom schemes\n                    const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');\n                    if (savedCustomSchemes) {\n                        try {\n                            const parsedCustomSchemes = JSON.parse(savedCustomSchemes);\n                            const customScheme = parsedCustomSchemes.find({\n                                \"ThemeProvider.useEffect.customScheme\": (s)=>s.id === savedSchemeId\n                            }[\"ThemeProvider.useEffect.customScheme\"]);\n                            if (customScheme) {\n                                setCurrentScheme(customScheme);\n                            }\n                        } catch (error) {\n                            console.error('Failed to find custom scheme:', error);\n                        }\n                    }\n                }\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Save theme to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            localStorage.setItem('dashboard-color-scheme', currentScheme.id);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        currentScheme\n    ]);\n    // Save custom schemes to localStorage when they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            localStorage.setItem('dashboard-custom-schemes', JSON.stringify(customSchemes));\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        customSchemes\n    ]);\n    const setColorScheme = (schemeId)=>{\n        // First check built-in schemes\n        const builtInScheme = COLOR_SCHEMES.find((s)=>s.id === schemeId);\n        if (builtInScheme) {\n            setCurrentScheme(builtInScheme);\n            return;\n        }\n        // Check custom schemes\n        const customScheme = customSchemes.find((s)=>s.id === schemeId);\n        if (customScheme) {\n            setCurrentScheme(customScheme);\n        }\n    };\n    const addCustomScheme = (scheme)=>{\n        setCustomSchemes((prev)=>{\n            const filtered = prev.filter((s)=>s.id !== scheme.id);\n            return [\n                ...filtered,\n                scheme\n            ];\n        });\n        setCurrentScheme(scheme);\n    };\n    const deleteCustomScheme = (schemeId)=>{\n        setCustomSchemes((prev)=>prev.filter((s)=>s.id !== schemeId));\n        // If the deleted scheme is currently active, switch to default\n        if (currentScheme.id === schemeId) {\n            setCurrentScheme(COLOR_SCHEMES[0]);\n        }\n    };\n    const resetToDefault = ()=>{\n        setCurrentScheme(COLOR_SCHEMES[0]);\n    };\n    // Get all schemes (built-in + custom)\n    const allSchemes = [\n        ...COLOR_SCHEMES,\n        ...customSchemes\n    ];\n    // Create dynamic Chakra UI theme based on current colors\n    const dynamicTheme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.extendTheme)({\n        ..._styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        colors: {\n            ..._styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"].colors,\n            brand: {\n                50: currentScheme.colors.primaryLight + '20',\n                100: currentScheme.colors.primaryLight + '40',\n                200: currentScheme.colors.primaryLight + '60',\n                300: currentScheme.colors.primaryLight + '80',\n                400: currentScheme.colors.primaryLight,\n                500: currentScheme.colors.primary,\n                600: currentScheme.colors.primaryDark,\n                700: currentScheme.colors.primaryDark + 'CC',\n                800: currentScheme.colors.primaryDark + 'AA',\n                900: currentScheme.colors.primaryDark + '88'\n            },\n            custom: {\n                primary: currentScheme.colors.primary,\n                primaryLight: currentScheme.colors.primaryLight,\n                primaryDark: currentScheme.colors.primaryDark,\n                secondary: currentScheme.colors.secondary,\n                accent: currentScheme.colors.accent,\n                background: currentScheme.colors.background,\n                surface: currentScheme.colors.surface,\n                text: currentScheme.colors.text,\n                textSecondary: currentScheme.colors.textSecondary,\n                border: currentScheme.colors.border,\n                success: currentScheme.colors.success,\n                warning: currentScheme.colors.warning,\n                error: currentScheme.colors.error,\n                info: currentScheme.colors.info\n            }\n        },\n        styles: {\n            global: {\n                body: {\n                    bg: currentScheme.colors.background,\n                    color: currentScheme.colors.text\n                }\n            }\n        }\n    });\n    const contextValue = {\n        currentScheme,\n        setColorScheme,\n        colorSchemes: allSchemes,\n        customSchemes,\n        addCustomScheme,\n        deleteCustomScheme,\n        resetToDefault\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ChakraProvider, {\n            theme: dynamicTheme,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 327,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./core/config.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__]);\n_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// @ts-nocheck\n\n\n\n\n// Create a wrapper component that uses useGuildInfo\nfunction AppContent({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"404 Bot Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContent, {\n                Component: Component,\n                pageProps: pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUEsY0FBYzs7QUFJb0M7QUFDckI7QUFDNEI7QUFFekQsb0RBQW9EO0FBQ3BELFNBQVNHLFdBQVcsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDMUMscUJBQ0U7OzBCQUNFLDhEQUFDSixrREFBSUE7O2tDQUNILDhEQUFDSztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBWUMsT0FBTTt3QkFBUUMsTUFBSzs7Ozs7O2tDQUNyRCw4REFBQ0o7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7d0JBQVlDLE9BQU07d0JBQVFDLE1BQUs7Ozs7OztrQ0FDckQsOERBQUNKO3dCQUFLQyxLQUFJO3dCQUFtQkUsT0FBTTt3QkFBVUMsTUFBSzs7Ozs7O2tDQUNsRCw4REFBQ0o7d0JBQUtDLEtBQUk7d0JBQVdHLE1BQUs7Ozs7Ozs7Ozs7OzswQkFFNUIsOERBQUNQO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7O0FBRzlCO0FBRWUsU0FBU08sSUFBSSxFQUFFUixTQUFTLEVBQUVDLFdBQVcsRUFBRVEsT0FBTyxFQUFFLEdBQUdSLFdBQVcsRUFBWTtJQUN2RixxQkFDRSw4REFBQ0wsNERBQWVBO1FBQUNhLFNBQVNBO2tCQUN4Qiw0RUFBQ1gsaUVBQWFBO3NCQUNaLDRFQUFDQztnQkFBV0MsV0FBV0E7Z0JBQVdDLFdBQVdBOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXJEIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxzcmNcXGRhc2hib2FyZFxccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEB0cy1ub2NoZWNrXHJcblxyXG5pbXBvcnQgeyBDb2xvck1vZGVTY3JpcHQgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcclxuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcclxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XHJcblxyXG4vLyBDcmVhdGUgYSB3cmFwcGVyIGNvbXBvbmVudCB0aGF0IHVzZXMgdXNlR3VpbGRJbmZvXHJcbmZ1bmN0aW9uIEFwcENvbnRlbnQoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxIZWFkPlxyXG4gICAgICAgIDx0aXRsZT40MDQgQm90IERhc2hib2FyZDwvdGl0bGU+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjMyeDMyXCIgaHJlZj1cIi9mYXZpY29uLTMyeDMyLnBuZ1wiIC8+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjE2eDE2XCIgaHJlZj1cIi9mYXZpY29uLTE2eDE2LnBuZ1wiIC8+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIHNpemVzPVwiMTgweDE4MFwiIGhyZWY9XCIvYXBwbGUtdG91Y2gtaWNvbi5wbmdcIiAvPlxyXG4gICAgICAgIDxsaW5rIHJlbD1cIm1hbmlmZXN0XCIgaHJlZj1cIi9zaXRlLndlYm1hbmlmZXN0XCIgLz5cclxuICAgICAgPC9IZWFkPlxyXG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSB9OiBBcHBQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8U2Vzc2lvblByb3ZpZGVyIHNlc3Npb249e3Nlc3Npb259PlxyXG4gICAgICA8VGhlbWVQcm92aWRlcj5cclxuICAgICAgICA8QXBwQ29udGVudCBDb21wb25lbnQ9e0NvbXBvbmVudH0gcGFnZVByb3BzPXtwYWdlUHJvcHN9IC8+XHJcbiAgICAgIDwvVGhlbWVQcm92aWRlcj5cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIkhlYWQiLCJUaGVtZVByb3ZpZGVyIiwiQXBwQ29udGVudCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsInRpdGxlIiwibGluayIsInJlbCIsInR5cGUiLCJzaXplcyIsImhyZWYiLCJBcHAiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBRTdDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcc3JjXFxkYXNoYm9hcmRcXHBhZ2VzXFxfZG9jdW1lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50J1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8SGVhZCAvPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8TWFpbiAvPlxyXG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvSHRtbD5cclxuICApXHJcbn0gIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(pages-dir-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/overview.tsx":
/*!****************************!*\
  !*** ./pages/overview.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Overview),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api/auth/[...nextauth] */ \"(pages-dir-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _config_cards__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../config/cards */ \"(pages-dir-node)/./config/cards.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/OverviewCard */ \"(pages-dir-node)/./components/OverviewCard.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Overview() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_7__.useSession)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"Overview.useEffect\": ()=>{\n            const fetchAnalytics = {\n                \"Overview.useEffect.fetchAnalytics\": async ()=>{\n                    try {\n                        const [serverRes, botRes] = await Promise.all([\n                            fetch('/api/analytics/server'),\n                            fetch('/api/analytics/bot')\n                        ]);\n                        if (!serverRes.ok || !botRes.ok) {\n                            throw new Error('Failed to fetch analytics');\n                        }\n                        const [serverData, botData] = await Promise.all([\n                            serverRes.json(),\n                            botRes.json()\n                        ]);\n                        setAnalyticsData({\n                            serverStats: serverData.serverStats,\n                            botStats: botData.botStats\n                        });\n                    } catch (error) {\n                        console.error('Error fetching analytics:', error);\n                        toast({\n                            title: 'Error',\n                            description: 'Failed to load analytics data',\n                            status: 'error',\n                            duration: 5000\n                        });\n                        // Fallback to mock data\n                        setAnalyticsData({\n                            serverStats: {\n                                totalMembers: 0,\n                                onlineMembers: 0,\n                                totalChannels: 0,\n                                totalRoles: 0\n                            },\n                            botStats: {\n                                commandsToday: 0,\n                                uptime: 'Unknown',\n                                responseTime: '0ms',\n                                activeAddons: 0,\n                                inactiveAddons: 0\n                            }\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Overview.useEffect.fetchAnalytics\"];\n            fetchAnalytics();\n        }\n    }[\"Overview.useEffect\"], [\n        toast\n    ]);\n    const quotes = [\n        '\"Talk is cheap. Show me the code.\" – Linus Torvalds',\n        '\"Programs must be written for people to read, and only incidentally for machines to execute.\" – Harold Abelson',\n        '\"Any fool can write code that a computer can understand. Good programmers write code that humans can understand.\" – Martin Fowler',\n        '\"First, solve the problem. Then, write the code.\" – John Johnson',\n        '\"404 Chill Not Found? Keep calm and debug on.\" – Unknown',\n        \"It's not a bug – it's an undocumented feature.\",\n        '\"The best error message is the one that never shows up.\" – Thomas Fuchs',\n        \"Code is like humor. When you have to explain it, it's bad.\",\n        '\"Experience is the name everyone gives to their mistakes.\" – Oscar Wilde',\n        '\"In order to be irreplaceable, one must always be different.\" – Coco Chanel'\n    ];\n    // Use a stable quote selection based on the day of the month\n    const getQuoteOfTheDay = ()=>{\n        const today = new Date();\n        const dayOfMonth = today.getDate(); // 1-31\n        return quotes[dayOfMonth % quotes.length];\n    };\n    const quoteOfTheDay = getQuoteOfTheDay();\n    // Filter cards based on user role (excluding specific overview cards)\n    const filteredCards = _config_cards__WEBPACK_IMPORTED_MODULE_5__.CARD_CONFIGS.filter((card)=>{\n        if (card.requiredRole === 'admin') {\n            return session?.user?.isAdmin;\n        }\n        if (card.requiredRole === 'moderator') {\n            return session?.user?.isModerator;\n        }\n        // Exclude 'overview', 'experimental', 'gameservers', 'applications', and 'tickets' cards\n        return ![\n            'overview',\n            'experimental',\n            'gameservers',\n            'applications',\n            'tickets'\n        ].includes(card.id);\n    });\n    // Chart data\n    const channelDistribution = analyticsData ? [\n        {\n            name: 'Text',\n            value: analyticsData.serverStats.textChannels || 0,\n            color: '#4299E1'\n        },\n        {\n            name: 'Voice',\n            value: analyticsData.serverStats.voiceChannels || 0,\n            color: '#48BB78'\n        },\n        {\n            name: 'Categories',\n            value: analyticsData.serverStats.categories || 0,\n            color: '#9F7AEA'\n        }\n    ] : [];\n    const orderedDays = [\n        'Mon',\n        'Tue',\n        'Wed',\n        'Thu',\n        'Fri',\n        'Sat',\n        'Sun'\n    ];\n    const defaultWeekly = orderedDays.map((day)=>({\n            day,\n            commands: 0,\n            joins: 0,\n            leaves: 0\n        }));\n    const weeklyActivity = analyticsData ? orderedDays.map((day)=>{\n        const botEntry = analyticsData.botStats?.weeklyActivity?.find((e)=>e.day === day) || {};\n        const memberEntry = analyticsData.serverStats?.weeklyMembers?.find((e)=>e.day === day) || {};\n        return {\n            day,\n            commands: botEntry.commands || 0,\n            joins: memberEntry.joins || 0,\n            leaves: memberEntry.leaves || 0\n        };\n    }) : defaultWeekly;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n            p: 8,\n            position: \"relative\",\n            _before: {\n                content: '\"\"',\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                width: '100%',\n                height: '100%',\n                background: 'radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)',\n                pointerEvents: 'none'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                    spacing: 8,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                            size: \"lg\",\n                            textAlign: \"center\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginRight: '0.5rem'\n                                    },\n                                    role: \"img\",\n                                    \"aria-label\": \"chart\",\n                                    children: \"\\uD83D\\uDCCA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                    as: \"span\",\n                                    bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                    bgClip: \"text\",\n                                    children: \"Server Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {\n                            columns: {\n                                base: 1,\n                                md: 2,\n                                lg: 4\n                            },\n                            spacing: 6,\n                            w: \"full\",\n                            children: isLoading ? // Loading skeletons\n                            Array.from({\n                                length: 4\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                            height: \"80px\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, this)\n                                }, i, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiUsers,\n                                                                color: \"blue.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Total Members\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.serverStats.totalMembers.toLocaleString() || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"green.400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiTrendingUp,\n                                                                mr: 1\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            analyticsData?.serverStats.onlineMembers || '0',\n                                                            \" online\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"green.300\",\n                                                        children: [\n                                                            \"+\",\n                                                            analyticsData?.serverStats.newMembersToday || 0,\n                                                            \" joined\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"red.400\",\n                                                        children: [\n                                                            \"-\",\n                                                            analyticsData?.serverStats.leftMembersToday || 0,\n                                                            \" left\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiMessageSquare,\n                                                                color: \"green.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Channels\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.serverStats.totalChannels || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"gray.400\",\n                                                        children: [\n                                                            analyticsData?.serverStats.totalRoles || '0',\n                                                            \" roles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity,\n                                                                color: \"purple.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Commands Today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.botStats.commandsToday || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"gray.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.responseTime || '0ms',\n                                                            \" avg response\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiServer,\n                                                                color: \"orange.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Bot Uptime\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"xl\",\n                                                        children: analyticsData?.botStats.uptime || 'Unknown'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"green.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.activeAddons || '0',\n                                                            \" addons active\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                        color: \"red.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.inactiveAddons || '0',\n                                                            \" addons inactive\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    analyticsData?.botStats.errorsToday > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_9___default()),\n                                        href: \"/admin/errors\",\n                                        _hover: {\n                                            textDecoration: 'none'\n                                        },\n                                        w: \"full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                            bg: \"whiteAlpha.100\",\n                                            backdropFilter: \"blur(10px)\",\n                                            borderColor: \"red.400\",\n                                            borderWidth: \"1px\",\n                                            cursor: \"pointer\",\n                                            _hover: {\n                                                transform: 'translateY(-4px)',\n                                                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',\n                                                borderColor: 'red.500'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Stat, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                    as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiAlertCircle,\n                                                                    color: \"red.400\",\n                                                                    boxSize: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatLabel, {\n                                                                    color: \"gray.300\",\n                                                                    children: \"Errors Today\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatNumber, {\n                                                            color: \"red.400\",\n                                                            fontSize: \"2xl\",\n                                                            children: analyticsData.botStats.errorsToday\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.StatHelpText, {\n                                                            color: \"red.300\",\n                                                            children: \"Needs attention\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                !isLoading && analyticsData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                    spacing: 8,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                            size: \"lg\",\n                            textAlign: \"center\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginRight: '0.5rem'\n                                    },\n                                    role: \"img\",\n                                    \"aria-label\": \"graph\",\n                                    children: \"\\uD83D\\uDCC8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                    as: \"span\",\n                                    bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                    bgClip: \"text\",\n                                    children: \"Activity Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {\n                            columns: {\n                                base: 1,\n                                lg: 2\n                            },\n                            spacing: 8,\n                            w: \"full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                            spacing: 4,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                    size: \"md\",\n                                                    color: \"white\",\n                                                    children: \"Channel Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                    h: \"200px\",\n                                                    w: \"full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Pie, {\n                                                                    data: channelDistribution,\n                                                                    cx: \"50%\",\n                                                                    cy: \"50%\",\n                                                                    innerRadius: 40,\n                                                                    outerRadius: 80,\n                                                                    paddingAngle: 5,\n                                                                    dataKey: \"value\",\n                                                                    children: channelDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                            fill: entry.color\n                                                                        }, `cell-${index}`, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                    wrapperStyle: {\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    contentStyle: {\n                                                                        backgroundColor: 'rgba(26, 32, 44, 0.9)',\n                                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                                        borderRadius: '8px',\n                                                                        color: '#fff'\n                                                                    },\n                                                                    itemStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    labelStyle: {\n                                                                        color: '#fff'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                    spacing: 4,\n                                                    justify: \"center\",\n                                                    children: channelDistribution.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                            spacing: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                    w: \"3\",\n                                                                    h: \"3\",\n                                                                    bg: item.color,\n                                                                    rounded: \"full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                    fontSize: \"sm\",\n                                                                    color: \"gray.300\",\n                                                                    children: [\n                                                                        item.name,\n                                                                        \": \",\n                                                                        item.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                            spacing: 4,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                    size: \"md\",\n                                                    color: \"white\",\n                                                    children: \"Weekly Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                    h: \"200px\",\n                                                    w: \"full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                                            data: weeklyActivity,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\",\n                                                                    stroke: \"rgba(255,255,255,0.1)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                                    dataKey: \"day\",\n                                                                    axisLine: false,\n                                                                    tickLine: false,\n                                                                    tick: {\n                                                                        fill: '#A0AEC0',\n                                                                        fontSize: 12\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                                                                    axisLine: false,\n                                                                    tickLine: false,\n                                                                    tick: {\n                                                                        fill: '#A0AEC0',\n                                                                        fontSize: 12\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                    wrapperStyle: {\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    contentStyle: {\n                                                                        backgroundColor: 'rgba(26, 32, 44, 0.9)',\n                                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                                        borderRadius: '8px',\n                                                                        color: '#fff'\n                                                                    },\n                                                                    itemStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    labelStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    cursor: {\n                                                                        fill: 'rgba(255,255,255,0.08)'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                                    dataKey: \"commands\",\n                                                                    fill: \"#4299E1\",\n                                                                    name: \"Commands\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                                    dataKey: \"joins\",\n                                                                    fill: \"#48BB78\",\n                                                                    name: \"Joins\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                                    dataKey: \"leaves\",\n                                                                    fill: \"#E53E3E\",\n                                                                    name: \"Leaves\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Wrap, {\n                    spacing: \"24px\",\n                    justify: \"start\",\n                    children: filteredCards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.WrapItem, {\n                            flex: \"1 0 260px\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__.OverviewCard, {\n                                ...card\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this)\n                        }, card.id, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\nconst getServerSideProps = async (ctx)=>{\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_3__.getServerSession)(ctx.req, ctx.res, _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_4__.authOptions);\n    if (!session) {\n        return {\n            redirect: {\n                destination: '/signin',\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {}\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/overview.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/theme.ts":
/*!*************************!*\
  !*** ./styles/theme.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// 1. Global theme configuration\nconst config = {\n    initialColorMode: 'dark',\n    useSystemColorMode: false\n};\n// 2. Custom brand color palette (indigo-violet tone)\nconst colors = {\n    brand: {\n        50: '#f5f3ff',\n        100: '#ede9fe',\n        200: '#ddd6fe',\n        300: '#c4b5fd',\n        400: '#a78bfa',\n        500: '#8b5cf6',\n        600: '#7c3aed',\n        700: '#6d28d9',\n        800: '#5b21b6',\n        900: '#4c1d95'\n    },\n    discord: {\n        50: '#e8e9fd',\n        100: '#d1d3fc',\n        200: '#b9bcfa',\n        300: '#a2a5f9',\n        400: '#8b8ef7',\n        500: '#5865F2',\n        600: '#4752c4',\n        700: '#363f97',\n        800: '#242c69',\n        900: '#12193c'\n    }\n};\n// 3. Extend the default theme\nconst theme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__.extendTheme)({\n    config,\n    fonts: {\n        heading: `'Inter', sans-serif`,\n        body: `'Inter', sans-serif`\n    },\n    colors,\n    styles: {\n        global: {\n            body: {\n                bg: 'gray.900',\n                color: 'gray.100'\n            }\n        }\n    },\n    components: {\n        Button: {\n            defaultProps: {\n                colorScheme: 'brand'\n            },\n            variants: {\n                solid: (props)=>({\n                        bg: `${props.colorScheme}.500`,\n                        color: 'white',\n                        _hover: {\n                            bg: `${props.colorScheme}.600`,\n                            transform: 'translateY(-2px)',\n                            boxShadow: 'lg'\n                        },\n                        _active: {\n                            bg: `${props.colorScheme}.700`,\n                            transform: 'translateY(0)'\n                        },\n                        transition: 'all 0.2s ease'\n                    })\n            }\n        },\n        Link: {\n            baseStyle: {\n                _hover: {\n                    textDecoration: 'none'\n                }\n            }\n        },\n        Box: {\n            baseStyle: {\n                transition: 'all 0.2s ease'\n            }\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3N0eWxlcy90aGVtZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFpRTtBQUVqRSxnQ0FBZ0M7QUFDaEMsTUFBTUMsU0FBc0I7SUFDMUJDLGtCQUFrQjtJQUNsQkMsb0JBQW9CO0FBQ3RCO0FBRUEscURBQXFEO0FBQ3JELE1BQU1DLFNBQVM7SUFDYkMsT0FBTztRQUNMLElBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7SUFDUDtJQUNBQyxTQUFTO1FBQ1AsSUFBSTtRQUNKLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztJQUNQO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDOUIsTUFBTUMsUUFBUVAsNkRBQVdBLENBQUM7SUFDeEJDO0lBQ0FPLE9BQU87UUFDTEMsU0FBUyxDQUFDLG1CQUFtQixDQUFDO1FBQzlCQyxNQUFNLENBQUMsbUJBQW1CLENBQUM7SUFDN0I7SUFDQU47SUFDQU8sUUFBUTtRQUNOQyxRQUFRO1lBQ05GLE1BQU07Z0JBQ0pHLElBQUk7Z0JBQ0pDLE9BQU87WUFDVDtRQUNGO0lBQ0Y7SUFDQUMsWUFBWTtRQUNWQyxRQUFRO1lBQ05DLGNBQWM7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBQyxVQUFVO2dCQUNSQyxPQUFPLENBQUNDLFFBQWdCO3dCQUN0QlIsSUFBSSxHQUFHUSxNQUFNSCxXQUFXLENBQUMsSUFBSSxDQUFDO3dCQUM5QkosT0FBTzt3QkFDUFEsUUFBUTs0QkFDTlQsSUFBSSxHQUFHUSxNQUFNSCxXQUFXLENBQUMsSUFBSSxDQUFDOzRCQUM5QkssV0FBVzs0QkFDWEMsV0FBVzt3QkFDYjt3QkFDQUMsU0FBUzs0QkFDUFosSUFBSSxHQUFHUSxNQUFNSCxXQUFXLENBQUMsSUFBSSxDQUFDOzRCQUM5QkssV0FBVzt3QkFDYjt3QkFDQUcsWUFBWTtvQkFDZDtZQUNGO1FBQ0Y7UUFDQUMsTUFBTTtZQUNKQyxXQUFXO2dCQUNUTixRQUFRO29CQUFFTyxnQkFBZ0I7Z0JBQU87WUFDbkM7UUFDRjtRQUNBQyxLQUFLO1lBQ0hGLFdBQVc7Z0JBQ1RGLFlBQVk7WUFDZDtRQUNGO0lBQ0Y7QUFDRjtBQUVBLGlFQUFlbkIsS0FBS0EsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcc3JjXFxkYXNoYm9hcmRcXHN0eWxlc1xcdGhlbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZXh0ZW5kVGhlbWUsIHR5cGUgVGhlbWVDb25maWcgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcclxuXHJcbi8vIDEuIEdsb2JhbCB0aGVtZSBjb25maWd1cmF0aW9uXHJcbmNvbnN0IGNvbmZpZzogVGhlbWVDb25maWcgPSB7XHJcbiAgaW5pdGlhbENvbG9yTW9kZTogJ2RhcmsnLFxyXG4gIHVzZVN5c3RlbUNvbG9yTW9kZTogZmFsc2UsXHJcbn07XHJcblxyXG4vLyAyLiBDdXN0b20gYnJhbmQgY29sb3IgcGFsZXR0ZSAoaW5kaWdvLXZpb2xldCB0b25lKVxyXG5jb25zdCBjb2xvcnMgPSB7XHJcbiAgYnJhbmQ6IHtcclxuICAgIDUwOiAgJyNmNWYzZmYnLFxyXG4gICAgMTAwOiAnI2VkZTlmZScsXHJcbiAgICAyMDA6ICcjZGRkNmZlJyxcclxuICAgIDMwMDogJyNjNGI1ZmQnLFxyXG4gICAgNDAwOiAnI2E3OGJmYScsXHJcbiAgICA1MDA6ICcjOGI1Y2Y2JywgLy8gcHJpbWFyeSBhY2NlbnRcclxuICAgIDYwMDogJyM3YzNhZWQnLFxyXG4gICAgNzAwOiAnIzZkMjhkOScsXHJcbiAgICA4MDA6ICcjNWIyMWI2JyxcclxuICAgIDkwMDogJyM0YzFkOTUnLFxyXG4gIH0sXHJcbiAgZGlzY29yZDoge1xyXG4gICAgNTA6ICcjZThlOWZkJyxcclxuICAgIDEwMDogJyNkMWQzZmMnLFxyXG4gICAgMjAwOiAnI2I5YmNmYScsXHJcbiAgICAzMDA6ICcjYTJhNWY5JyxcclxuICAgIDQwMDogJyM4YjhlZjcnLFxyXG4gICAgNTAwOiAnIzU4NjVGMicsIC8vIERpc2NvcmQgYnJhbmQgY29sb3JcclxuICAgIDYwMDogJyM0NzUyYzQnLFxyXG4gICAgNzAwOiAnIzM2M2Y5NycsXHJcbiAgICA4MDA6ICcjMjQyYzY5JyxcclxuICAgIDkwMDogJyMxMjE5M2MnLFxyXG4gIH0sXHJcbn07XHJcblxyXG4vLyAzLiBFeHRlbmQgdGhlIGRlZmF1bHQgdGhlbWVcclxuY29uc3QgdGhlbWUgPSBleHRlbmRUaGVtZSh7XHJcbiAgY29uZmlnLFxyXG4gIGZvbnRzOiB7XHJcbiAgICBoZWFkaW5nOiBgJ0ludGVyJywgc2Fucy1zZXJpZmAsXHJcbiAgICBib2R5OiBgJ0ludGVyJywgc2Fucy1zZXJpZmAsXHJcbiAgfSxcclxuICBjb2xvcnMsXHJcbiAgc3R5bGVzOiB7XHJcbiAgICBnbG9iYWw6IHtcclxuICAgICAgYm9keToge1xyXG4gICAgICAgIGJnOiAnZ3JheS45MDAnLFxyXG4gICAgICAgIGNvbG9yOiAnZ3JheS4xMDAnLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICB9LFxyXG4gIGNvbXBvbmVudHM6IHtcclxuICAgIEJ1dHRvbjoge1xyXG4gICAgICBkZWZhdWx0UHJvcHM6IHtcclxuICAgICAgICBjb2xvclNjaGVtZTogJ2JyYW5kJyxcclxuICAgICAgfSxcclxuICAgICAgdmFyaWFudHM6IHtcclxuICAgICAgICBzb2xpZDogKHByb3BzOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICBiZzogYCR7cHJvcHMuY29sb3JTY2hlbWV9LjUwMGAsXHJcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcclxuICAgICAgICAgIF9ob3Zlcjoge1xyXG4gICAgICAgICAgICBiZzogYCR7cHJvcHMuY29sb3JTY2hlbWV9LjYwMGAsXHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVkoLTJweCknLFxyXG4gICAgICAgICAgICBib3hTaGFkb3c6ICdsZycsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgX2FjdGl2ZToge1xyXG4gICAgICAgICAgICBiZzogYCR7cHJvcHMuY29sb3JTY2hlbWV9LjcwMGAsXHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVkoMCknLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcclxuICAgICAgICB9KSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBMaW5rOiB7XHJcbiAgICAgIGJhc2VTdHlsZToge1xyXG4gICAgICAgIF9ob3ZlcjogeyB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgQm94OiB7XHJcbiAgICAgIGJhc2VTdHlsZToge1xyXG4gICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgfSxcclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCB0aGVtZTsgIl0sIm5hbWVzIjpbImV4dGVuZFRoZW1lIiwiY29uZmlnIiwiaW5pdGlhbENvbG9yTW9kZSIsInVzZVN5c3RlbUNvbG9yTW9kZSIsImNvbG9ycyIsImJyYW5kIiwiZGlzY29yZCIsInRoZW1lIiwiZm9udHMiLCJoZWFkaW5nIiwiYm9keSIsInN0eWxlcyIsImdsb2JhbCIsImJnIiwiY29sb3IiLCJjb21wb25lbnRzIiwiQnV0dG9uIiwiZGVmYXVsdFByb3BzIiwiY29sb3JTY2hlbWUiLCJ2YXJpYW50cyIsInNvbGlkIiwicHJvcHMiLCJfaG92ZXIiLCJ0cmFuc2Zvcm0iLCJib3hTaGFkb3ciLCJfYWN0aXZlIiwidHJhbnNpdGlvbiIsIkxpbmsiLCJiYXNlU3R5bGUiLCJ0ZXh0RGVjb3JhdGlvbiIsIkJveCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./styles/theme.ts\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/index.js":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/index.js ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bar: () => (/* reexport safe */ _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__.Bar),\n/* harmony export */   BarChart: () => (/* reexport safe */ _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__.BarChart),\n/* harmony export */   CartesianGrid: () => (/* reexport safe */ _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__.CartesianGrid),\n/* harmony export */   Cell: () => (/* reexport safe */ _component_Cell__WEBPACK_IMPORTED_MODULE_3__.Cell),\n/* harmony export */   Pie: () => (/* reexport safe */ _polar_Pie__WEBPACK_IMPORTED_MODULE_4__.Pie),\n/* harmony export */   PieChart: () => (/* reexport safe */ _chart_PieChart__WEBPACK_IMPORTED_MODULE_5__.PieChart),\n/* harmony export */   ResponsiveContainer: () => (/* reexport safe */ _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer),\n/* harmony export */   Tooltip: () => (/* reexport safe */ _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__.Tooltip),\n/* harmony export */   XAxis: () => (/* reexport safe */ _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__.XAxis),\n/* harmony export */   YAxis: () => (/* reexport safe */ _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__.YAxis)\n/* harmony export */ });\n/* harmony import */ var _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cartesian/Bar */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chart/BarChart */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian/CartesianGrid */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _component_Cell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./component/Cell */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _polar_Pie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./polar/Pie */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _chart_PieChart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chart/PieChart */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./component/ResponsiveContainer */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./component/Tooltip */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./cartesian/XAxis */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./cartesian/YAxis */ \"(pages-dir-node)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/cartesian/YAxis.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUJhcixCYXJDaGFydCxDYXJ0ZXNpYW5HcmlkLENlbGwsUGllLFBpZUNoYXJ0LFJlc3BvbnNpdmVDb250YWluZXIsVG9vbHRpcCxYQXhpcyxZQXhpcyE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWNoYXJ0c0AzLjEuMF9AdHlwZXMrcmVhY3RfMjRlNGZkNWVhZmRmOWY1OGQwNGFlMWIyY2UxZDZhMTUvbm9kZV9tb2R1bGVzL3JlY2hhcnRzL2VzNi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNxQztBQUNNO0FBQ2M7QUFDbEI7QUFDTjtBQUNVO0FBQzBCO0FBQ3hCO0FBQ0o7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1XFxub2RlX21vZHVsZXNcXHJlY2hhcnRzXFxlczZcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgQmFyIH0gZnJvbSBcIi4vY2FydGVzaWFuL0JhclwiXG5leHBvcnQgeyBCYXJDaGFydCB9IGZyb20gXCIuL2NoYXJ0L0JhckNoYXJ0XCJcbmV4cG9ydCB7IENhcnRlc2lhbkdyaWQgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vQ2FydGVzaWFuR3JpZFwiXG5leHBvcnQgeyBDZWxsIH0gZnJvbSBcIi4vY29tcG9uZW50L0NlbGxcIlxuZXhwb3J0IHsgUGllIH0gZnJvbSBcIi4vcG9sYXIvUGllXCJcbmV4cG9ydCB7IFBpZUNoYXJ0IH0gZnJvbSBcIi4vY2hhcnQvUGllQ2hhcnRcIlxuZXhwb3J0IHsgUmVzcG9uc2l2ZUNvbnRhaW5lciB9IGZyb20gXCIuL2NvbXBvbmVudC9SZXNwb25zaXZlQ29udGFpbmVyXCJcbmV4cG9ydCB7IFRvb2x0aXAgfSBmcm9tIFwiLi9jb21wb25lbnQvVG9vbHRpcFwiXG5leHBvcnQgeyBYQXhpcyB9IGZyb20gXCIuL2NhcnRlc2lhbi9YQXhpc1wiXG5leHBvcnQgeyBZQXhpcyB9IGZyb20gXCIuL2NhcnRlc2lhbi9ZQXhpc1wiIl0sIm5hbWVzIjpbIkJhciIsIkJhckNoYXJ0IiwiQ2FydGVzaWFuR3JpZCIsIkNlbGwiLCJQaWUiLCJQaWVDaGFydCIsIlJlc3BvbnNpdmVDb250YWluZXIiLCJUb29sdGlwIiwiWEF4aXMiLCJZQXhpcyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQWN0aXZpdHksRmlBbGVydENpcmNsZSxGaU1lc3NhZ2VTcXVhcmUsRmlTZXJ2ZXIsRmlUcmVuZGluZ1VwLEZpVXNlcnMhPSEuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1pY29ucy9maS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEoiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pY29uc1xcZmlcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiRDpcXFxcVXNlcnNcXFxcUGV0ZSBHYW1pbmcgUENcXFxcRGVza3RvcFxcXFw0MDQgQm90XFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxyZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWljb25zXFxcXGZpXFxcXGluZGV4Lm1qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQWN0aXZpdHksRmlIZWxwQ2lyY2xlLEZpTG9jayxGaU1vbml0b3IsRmlQYWNrYWdlLEZpU2V0dGluZ3MhPSEuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1pY29ucy9maS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEoiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pY29uc1xcZmlcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiRDpcXFxcVXNlcnNcXFxcUGV0ZSBHYW1pbmcgUENcXFxcRGVza3RvcFxcXFw0MDQgQm90XFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxyZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWljb25zXFxcXGZpXFxcXGluZGV4Lm1qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQWxlcnRDaXJjbGUsRmlCb3gsRmlDaGV2cm9uRG93bixGaUhlbHBDaXJjbGUsRmlIb21lLEZpTW9uaXRvcixGaVBhY2thZ2UsRmlTZXJ2ZXIsRmlTZXR0aW5ncyE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!****************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQmVsbCE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpTG9nT3V0IT0hLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvcmVhY3QtaWNvbnMvZmkvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBKIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtaWNvbnNcXGZpXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkQ6XFxcXFVzZXJzXFxcXFBldGUgR2FtaW5nIFBDXFxcXERlc2t0b3BcXFxcNDA0IEJvdFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxcXG5vZGVfbW9kdWxlc1xcXFxyZWFjdC1pY29uc1xcXFxmaVxcXFxpbmRleC5tanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15","vendor-chunks/d3-array@3.2.4","vendor-chunks/d3-shape@3.2.0","vendor-chunks/es-toolkit@1.39.7","vendor-chunks/d3-interpolate@3.0.1","vendor-chunks/d3-scale@4.0.2","vendor-chunks/d3-format@3.1.0","vendor-chunks/d3-time@3.1.0","vendor-chunks/d3-color@3.1.0","vendor-chunks/use-sync-external-store@1.5.0_react@19.1.0","vendor-chunks/react-icons@5.5.0_react@19.1.0","vendor-chunks/d3-time-format@4.1.0","vendor-chunks/d3-path@3.1.0","vendor-chunks/victory-vendor@37.3.6","vendor-chunks/react-is@18.3.1","vendor-chunks/eventemitter3@5.0.1","vendor-chunks/tiny-invariant@1.3.3","vendor-chunks/reselect@5.1.1","vendor-chunks/redux@5.0.1","vendor-chunks/redux-thunk@3.1.0_redux@5.0.1","vendor-chunks/react-redux@9.2.0_@types+re_be3841c7b2122d7ded6acb16e6acc76c","vendor-chunks/internmap@2.0.3","vendor-chunks/immer@10.1.1","vendor-chunks/clsx@2.1.1","vendor-chunks/@reduxjs+toolkit@2.8.2_reac_13e03a16b3880bf31db5b9783f72eaa4","vendor-chunks/decimal.js-light@2.5.1"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Coverview.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
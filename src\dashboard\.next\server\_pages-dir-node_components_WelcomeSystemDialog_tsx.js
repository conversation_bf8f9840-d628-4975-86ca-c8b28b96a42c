"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_WelcomeSystemDialog_tsx";
exports.ids = ["_pages-dir-node_components_WelcomeSystemDialog_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/WelcomeSystemDialog.tsx":
/*!********************************************!*\
  !*** ./components/WelcomeSystemDialog.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WelcomeSystemDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCopy,FiHash,FiLogOut,FiMessageSquare,FiPlus,FiSettings,FiShuffle,FiTrash2!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiCopy,FiHash,FiLogOut,FiMessageSquare,FiPlus,FiSettings,FiShuffle,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\nconst defaultSettings = {\n    welcome: {\n        enabled: false,\n        channelId: '',\n        messages: [\n            {\n                title: \"🎮 Welcome to {guild}, {userName}!\",\n                description: \"Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\\n\\n📅 Account Created: {UserCreation}\\n🎯 Join Date: {joinDate}\",\n                color: \"#FF6B35\",\n                footer: \"You're our {memberCount} member - let's get weird!\"\n            },\n            {\n                title: \"🚨 Error 404: Chill Not Found! 🚨\",\n                description: \"Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\\n\\n🎮 Member #{memberCountNumeric}\\n📅 Joined: {joinTime}\",\n                color: \"#8B0000\",\n                footer: \"Welcome to the madness!\"\n            },\n            {\n                title: \"🎯 Player {userName} has entered the game!\",\n                description: \"Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\\n\\n📅 Account Age: {UserCreation}\\n🎯 Member Since: {user-joinedAt}\",\n                color: \"#1E90FF\",\n                footer: \"Time to level up your social life!\"\n            },\n            {\n                title: \"🔥 New challenger has appeared!\",\n                description: \"It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\\n\\n🎮 Member #{memberCountNumeric}\\n⏰ Joined: {joinTime}\",\n                color: \"#DC143C\",\n                footer: \"Ready to game and chill?\"\n            },\n            {\n                title: \"🎲 Welcome to the chaos, {userName}!\",\n                description: \"You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\\n\\n📅 Account Created: {user-createdAt}\\n🎯 Join Time: {joinTime}\",\n                color: \"#9932CC\",\n                footer: \"Let the games begin!\"\n            },\n            {\n                title: \"⚡ Level Up! New member unlocked!\",\n                description: \"{user} just joined the {guild} crew! We promise we're friendlier than our name suggests... mostly.\\n\\n🎮 You're member #{memberCountNumeric}\\n⏰ Account Age: {UserCreation}\",\n                color: \"#FFD700\",\n                footer: \"Achievement unlocked: Found the cool kids table\"\n            },\n            {\n                title: \"🎪 Welcome to the circus, {userName}!\",\n                description: \"Hope you're ready for some adult gaming fun and conversations that would make your mother question your life choices.\\n\\n📅 Joined: {joinDate}\\n🎯 Member #{memberCountNumeric}\",\n                color: \"#FF1493\",\n                footer: \"We're all mad here, but in a good way!\"\n            },\n            {\n                title: \"🎨 Another wild gamer appears!\",\n                description: \"Hey {user}! Welcome to {server} where we game hard, laugh harder, and occasionally make sense.\\n\\n📅 Account Created: {UserCreation}\\n⏰ Join Time: {joinTime}\",\n                color: \"#20B2AA\",\n                footer: \"Time to make some questionable decisions together!\"\n            },\n            {\n                title: \"🚀 Mission accepted: Welcome {userName}!\",\n                description: \"You've successfully infiltrated {guild}. Your mission: have fun, play games, and embrace the chaos.\\n\\n🎮 Member #{memberCountNumeric}\\n📅 Join Date: {joinDate}\",\n                color: \"#4169E1\",\n                footer: \"Good luck, you'll need it!\"\n            },\n            {\n                title: \"🎯 Critical hit! New member joined!\",\n                description: \"Welcome {user} to our den of gaming degeneracy and adult conversation. Check your sanity at the door!\\n\\n📅 Account Age: {UserCreation}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#32CD32\",\n                footer: \"RIP your free time\"\n            }\n        ],\n        autoRole: {\n            enabled: false,\n            roleIds: [],\n            delay: 1000,\n            retry: {\n                enabled: true,\n                maxAttempts: 3,\n                delayBetweenAttempts: 5000\n            }\n        },\n        nekosGif: {\n            enabled: true,\n            type: 'wave' // wave, hug, pat, etc.\n        }\n    },\n    goodbye: {\n        enabled: false,\n        channelId: '',\n        messages: [\n            {\n                title: \"💀 Game Over for {userName}\",\n                description: \"Looks like {userName} has rage quit from {server}.\\nThanks for the memories and questionable life choices!\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#8B0000\",\n                footer: \"Press F to pay respects\"\n            },\n            {\n                title: \"🚪 {userName} has left the building\",\n                description: \"Another one bites the dust! {userName} decided our chaos wasn't for them.\\nCan't win 'em all, I guess.\\n\\n{kickStatus}\\n🕒 Left: {leaveTime}\",\n                color: \"#696969\",\n                footer: \"The door's always open... maybe\"\n            },\n            {\n                title: \"📤 Connection lost: {userName}\",\n                description: \"{userName} has disconnected from {guild}.\\nHope they found what they were looking for!\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#FF6347\",\n                footer: \"Thanks for gaming with us!\"\n            },\n            {\n                title: \"🎭 Plot twist: {userName} vanished!\",\n                description: \"In a shocking turn of events, {userName} has left {server}.\\nThe show must go on!\\n\\n{kickStatus}\\n🕒 Left: {leaveTime}\",\n                color: \"#4B0082\",\n                footer: \"Until we meet again in another lobby\"\n            },\n            {\n                title: \"🏃‍♂️ {userName} speed-ran their exit\",\n                description: \"Well, that was quick! {userName} decided to bounce from {server}.\\nNo hard feelings... probably.\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#FF8C00\",\n                footer: \"See ya, wouldn't wanna be ya!\"\n            },\n            {\n                title: \"🎪 The circus lost a performer\",\n                description: \"{userName} has left the madness that is {server}.\\nHope they find their chill somewhere else!\\n\\n{kickStatus}\\n🕒 Left: {leaveTime}\",\n                color: \"#20B2AA\",\n                footer: \"Thanks for adding to the chaos!\"\n            },\n            {\n                title: \"💥 {userName} signed off\",\n                description: \"Another gamer has left {guild}.\\nMay your framerate be high and your ping be low!\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#FF69B4\",\n                footer: \"GG, no re\"\n            },\n            {\n                title: \"🎮 Player {userName} has disconnected\",\n                description: \"Looks like {userName} found the exit door in {server}.\\nHope our brand of chaos was entertaining!\\n\\n{kickStatus}\\n🕒 Left: {leaveTime}\",\n                color: \"#32CD32\",\n                footer: \"Thanks for playing with us!\"\n            },\n            {\n                title: \"🚀 {userName} has left orbit\",\n                description: \"Mission complete! {userName} has successfully escaped {server}.\\nSafe travels, space cadet!\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n                color: \"#1E90FF\",\n                footer: \"Houston, we have a departure\"\n            },\n            {\n                title: \"🎯 Target eliminated: {userName}\",\n                description: \"{userName} has been removed from the game... wait, they left voluntarily.\\nWell, that's less dramatic than expected.\\n\\n{kickStatus}\\n🕒 Left: {leaveTime}\",\n                color: \"#B22222\",\n                footer: \"Better luck next server!\"\n            }\n        ],\n        nekosGif: {\n            enabled: true,\n            type: 'cry' // cry, wave, sad, etc.\n        }\n    }\n};\n// Placeholder information for help\nconst PLACEHOLDERS = {\n    user: [\n        {\n            name: '{user}',\n            description: 'Mentions the user, e.g., @Username'\n        },\n        {\n            name: '{userName}',\n            description: 'The user\\'s name, e.g., Username'\n        },\n        {\n            name: '{userTag}',\n            description: 'The user\\'s tag, e.g., Username#1234'\n        },\n        {\n            name: '{userId}',\n            description: 'The user\\'s ID, e.g., 123456789012345678'\n        },\n        {\n            name: '{userBanner}',\n            description: 'URL of the user\\'s banner (if they have one)'\n        },\n        {\n            name: '{user-createdAt}',\n            description: 'The date the user\\'s account was created'\n        },\n        {\n            name: '{UserCreation}',\n            description: 'How long ago the user\\'s account was created (e.g., \"2 years ago\")'\n        }\n    ],\n    guild: [\n        {\n            name: '{server} / {guild}',\n            description: 'The server\\'s name'\n        },\n        {\n            name: '{guildIcon}',\n            description: 'URL of the server\\'s icon'\n        },\n        {\n            name: '{memberCount}',\n            description: 'Total members with ordinal suffix, e.g., \"100th\"'\n        },\n        {\n            name: '{memberCountNumeric}',\n            description: 'Total members as a number, e.g., \"100\"'\n        }\n    ],\n    time: [\n        {\n            name: '{longTime}',\n            description: 'Current date and time, e.g., \"June 1, 2024 12:00 PM\"'\n        },\n        {\n            name: '{shortTime}',\n            description: 'Current time, e.g., \"12:00 PM\"'\n        }\n    ],\n    welcome: [\n        {\n            name: '{joinDate}',\n            description: 'The date the user joined'\n        },\n        {\n            name: '{joinTime}',\n            description: 'The time the user joined'\n        },\n        {\n            name: '{user-joinedAt}',\n            description: 'Full date and time the user joined'\n        }\n    ],\n    goodbye: [\n        {\n            name: '{leaveDate}',\n            description: 'The date the user left'\n        },\n        {\n            name: '{leaveTime}',\n            description: 'The time the user left'\n        },\n        {\n            name: '{kickStatus}',\n            description: 'If the user was kicked/banned, shows the status. Otherwise, hidden.'\n        }\n    ]\n};\nfunction WelcomeSystemDialog({ isOpen, onClose, channels = [], roles = [] }) {\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(defaultSettings);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedWelcomeMessage, setSelectedWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [selectedGoodbyeMessage, setSelectedGoodbyeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [showPlaceholders, setShowPlaceholders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Include text channels and announcement channels as they can both receive messages\n    // Channel types can be either numbers or strings depending on the API response\n    const textChannels = channels.filter((c)=>c.type === 0 || c.type === 5 || c.type === 'GUILD_TEXT' || c.type === 'GUILD_ANNOUNCEMENT');\n    const manageableRoles = roles.filter((r)=>r.name !== '@everyone');\n    const fetchSettings = async ()=>{\n        setIsLoading(true);\n        try {\n            const res = await fetch('/api/automation/welcome');\n            if (!res.ok) throw new Error('Failed to fetch settings');\n            const data = await res.json();\n            setSettings(data);\n        } catch (error) {\n            toast({\n                title: 'Error loading settings',\n                description: error.message,\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const res = await fetch('/api/automation/welcome', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            if (!res.ok) throw new Error('Failed to save settings');\n            toast({\n                title: 'Settings Saved',\n                status: 'success',\n                duration: 3000\n            });\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error saving settings',\n                description: error.message,\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WelcomeSystemDialog.useEffect\": ()=>{\n            if (isOpen) {\n                fetchSettings();\n            }\n        }\n    }[\"WelcomeSystemDialog.useEffect\"], [\n        isOpen\n    ]);\n    // Helper functions for managing settings\n    const handleWelcomeChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    [field]: value\n                }\n            }));\n    };\n    const handleGoodbyeChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    [field]: value\n                }\n            }));\n    };\n    const handleWelcomeNekosChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    nekosGif: {\n                        ...prev.welcome.nekosGif,\n                        [field]: value\n                    }\n                }\n            }));\n    };\n    const handleGoodbyeNekosChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    nekosGif: {\n                        ...prev.goodbye.nekosGif,\n                        [field]: value\n                    }\n                }\n            }));\n    };\n    const handleWelcomeAutoRoleChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    autoRole: {\n                        ...prev.welcome.autoRole,\n                        [field]: value\n                    }\n                }\n            }));\n    };\n    const handleWelcomeAutoRoleRetryChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    autoRole: {\n                        ...prev.welcome.autoRole,\n                        retry: {\n                            ...prev.welcome.autoRole.retry,\n                            [field]: value\n                        }\n                    }\n                }\n            }));\n    };\n    // Message template management\n    const addWelcomeMessage = ()=>{\n        const newMessage = {\n            title: \"🎮 Welcome to {guild}, {userName}!\",\n            description: \"Welcome to our server! We're glad to have you here.\\n\\n📅 Account Created: {UserCreation}\\n🎯 Join Date: {joinDate}\",\n            color: \"#00FF00\",\n            footer: \"You're our {memberCount} member!\"\n        };\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    messages: [\n                        ...prev.welcome.messages,\n                        newMessage\n                    ]\n                }\n            }));\n        setSelectedWelcomeMessage(settings.welcome.messages.length);\n    };\n    const addGoodbyeMessage = ()=>{\n        const newMessage = {\n            title: \"👋 Goodbye {userName}\",\n            description: \"Thanks for being part of {guild}! We'll miss you.\\n\\n{kickStatus}\\n⏰ Member Since: {user-joinedAt}\",\n            color: \"#FF0000\",\n            footer: \"Safe travels!\"\n        };\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    messages: [\n                        ...prev.goodbye.messages,\n                        newMessage\n                    ]\n                }\n            }));\n        setSelectedGoodbyeMessage(settings.goodbye.messages.length);\n    };\n    const removeWelcomeMessage = (index)=>{\n        if (settings.welcome.messages.length <= 1) {\n            toast({\n                title: 'Cannot delete',\n                description: 'You must have at least one welcome message.',\n                status: 'warning',\n                duration: 3000\n            });\n            return;\n        }\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    messages: prev.welcome.messages.filter((_, i)=>i !== index)\n                }\n            }));\n        if (selectedWelcomeMessage >= settings.welcome.messages.length - 1) {\n            setSelectedWelcomeMessage(Math.max(0, selectedWelcomeMessage - 1));\n        }\n    };\n    const removeGoodbyeMessage = (index)=>{\n        if (settings.goodbye.messages.length <= 1) {\n            toast({\n                title: 'Cannot delete',\n                description: 'You must have at least one goodbye message.',\n                status: 'warning',\n                duration: 3000\n            });\n            return;\n        }\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    messages: prev.goodbye.messages.filter((_, i)=>i !== index)\n                }\n            }));\n        if (selectedGoodbyeMessage >= settings.goodbye.messages.length - 1) {\n            setSelectedGoodbyeMessage(Math.max(0, selectedGoodbyeMessage - 1));\n        }\n    };\n    const updateWelcomeMessage = (index, field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    messages: prev.welcome.messages.map((msg, i)=>i === index ? {\n                            ...msg,\n                            [field]: value\n                        } : msg)\n                }\n            }));\n    };\n    const updateGoodbyeMessage = (index, field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    messages: prev.goodbye.messages.map((msg, i)=>i === index ? {\n                            ...msg,\n                            [field]: value\n                        } : msg)\n                }\n            }));\n    };\n    const duplicateWelcomeMessage = (index)=>{\n        const messageToDuplicate = {\n            ...settings.welcome.messages[index]\n        };\n        messageToDuplicate.title = `${messageToDuplicate.title} (Copy)`;\n        setSettings((prev)=>({\n                ...prev,\n                welcome: {\n                    ...prev.welcome,\n                    messages: [\n                        ...prev.welcome.messages,\n                        messageToDuplicate\n                    ]\n                }\n            }));\n        setSelectedWelcomeMessage(settings.welcome.messages.length);\n    };\n    const duplicateGoodbyeMessage = (index)=>{\n        const messageToDuplicate = {\n            ...settings.goodbye.messages[index]\n        };\n        messageToDuplicate.title = `${messageToDuplicate.title} (Copy)`;\n        setSettings((prev)=>({\n                ...prev,\n                goodbye: {\n                    ...prev.goodbye,\n                    messages: [\n                        ...prev.goodbye.messages,\n                        messageToDuplicate\n                    ]\n                }\n            }));\n        setSelectedGoodbyeMessage(settings.goodbye.messages.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"6xl\",\n        scrollBehavior: \"inside\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalOverlay, {\n                bg: \"blackAlpha.600\",\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalContent, {\n                maxH: \"90vh\",\n                bg: currentScheme.colors.background,\n                border: \"2px solid\",\n                borderColor: \"green.400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                    as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMessageSquare\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    children: \"Advanced Welcome & Goodbye System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalBody, {\n                        pb: 6,\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                            justify: \"center\",\n                            h: \"400px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Spinner, {\n                                    size: \"xl\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    children: \"Loading Settings...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tabs, {\n                            isFitted: true,\n                            variant: \"enclosed\",\n                            colorScheme: \"blue\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                    as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMessageSquare,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" Welcome Messages\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                    as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLogOut,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" Goodbye Messages\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                    as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" Placeholders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: \"Welcome System\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                            size: \"lg\",\n                                                                            isChecked: settings.welcome.enabled,\n                                                                            onChange: (e)=>handleWelcomeChange('enabled', e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                        isDisabled: !settings.welcome.enabled,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiHash\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 550,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                            children: \"Welcome Channel\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 551,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                                                                                placeholder: \"Select a channel\",\n                                                                                value: settings.welcome.channelId || '',\n                                                                                onChange: (e)=>handleWelcomeChange('channelId', e.target.value),\n                                                                                children: textChannels.map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: channel.id,\n                                                                                        children: [\n                                                                                            \"#\",\n                                                                                            channel.name\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 560,\n                                                                                        columnNumber: 35\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 554,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            textChannels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.500\",\n                                                                                mt: 1,\n                                                                                children: \"No text channels found. Make sure the bot has permission to view channels.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 566,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        opacity: !settings.welcome.enabled ? 0.6 : 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            align: \"start\",\n                                                                            spacing: 1,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: \"Welcome Message Templates\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create multiple templates - one will be randomly selected for each new member\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"subtle\",\n                                                                                    children: [\n                                                                                        settings.welcome.messages.length,\n                                                                                        \" template\",\n                                                                                        settings.welcome.messages.length !== 1 ? 's' : ''\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 590,\n                                                                                        columnNumber: 43\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: addWelcomeMessage,\n                                                                                    isDisabled: !settings.welcome.enabled,\n                                                                                    children: \"Add Template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                            spacing: 2,\n                                                                            wrap: \"wrap\",\n                                                                            children: settings.welcome.messages.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: selectedWelcomeMessage === index ? \"solid\" : \"outline\",\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>setSelectedWelcomeMessage(index),\n                                                                                    isDisabled: !settings.welcome.enabled,\n                                                                                    children: [\n                                                                                        \"Template \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        settings.welcome.messages[selectedWelcomeMessage] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                            p: 4,\n                                                                            borderWidth: 1,\n                                                                            borderRadius: \"md\",\n                                                                            bg: \"gray.50\",\n                                                                            _dark: {\n                                                                                bg: \"gray.800\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                spacing: 4,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                fontWeight: \"bold\",\n                                                                                                children: [\n                                                                                                    \"Template \",\n                                                                                                    selectedWelcomeMessage + 1\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 624,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                                                                                        label: \"Duplicate Template\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                            \"aria-label\": \"Duplicate template\",\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 629,\n                                                                                                                columnNumber: 49\n                                                                                                            }, void 0),\n                                                                                                            size: \"sm\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            onClick: ()=>duplicateWelcomeMessage(selectedWelcomeMessage),\n                                                                                                            isDisabled: !settings.welcome.enabled\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 627,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 626,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                                                                                        label: \"Delete Template\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                            \"aria-label\": \"Delete template\",\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrash2, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 639,\n                                                                                                                columnNumber: 49\n                                                                                                            }, void 0),\n                                                                                                            size: \"sm\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            colorScheme: \"red\",\n                                                                                                            onClick: ()=>removeWelcomeMessage(selectedWelcomeMessage),\n                                                                                                            isDisabled: !settings.welcome.enabled || settings.welcome.messages.length <= 1\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 637,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 636,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 625,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                        isDisabled: !settings.welcome.enabled,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                children: \"Embed Title\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 651,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                value: settings.welcome.messages[selectedWelcomeMessage]?.title || '',\n                                                                                                onChange: (e)=>updateWelcomeMessage(selectedWelcomeMessage, 'title', e.target.value),\n                                                                                                placeholder: \"e.g., \\uD83C\\uDFAE Welcome to {guild}, {userName}!\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 652,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 650,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                        isDisabled: !settings.welcome.enabled,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                children: \"Embed Description\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 660,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Textarea, {\n                                                                                                value: settings.welcome.messages[selectedWelcomeMessage]?.description || '',\n                                                                                                onChange: (e)=>updateWelcomeMessage(selectedWelcomeMessage, 'description', e.target.value),\n                                                                                                placeholder: \"Enter your welcome message description...\",\n                                                                                                rows: 6\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 661,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 659,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        spacing: 4,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                isDisabled: !settings.welcome.enabled,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                        children: \"Embed Color\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 671,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                        type: \"color\",\n                                                                                                        value: settings.welcome.messages[selectedWelcomeMessage]?.color || '#00FF00',\n                                                                                                        onChange: (e)=>updateWelcomeMessage(selectedWelcomeMessage, 'color', e.target.value),\n                                                                                                        w: \"100px\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 672,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 670,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                isDisabled: !settings.welcome.enabled,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                        children: \"Embed Footer\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 680,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                        value: settings.welcome.messages[selectedWelcomeMessage]?.footer || '',\n                                                                                                        onChange: (e)=>updateWelcomeMessage(selectedWelcomeMessage, 'footer', e.target.value),\n                                                                                                        placeholder: \"e.g., You're our {memberCount} member!\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 681,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 679,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 669,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        opacity: !settings.welcome.enabled ? 0.6 : 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            align: \"start\",\n                                                                            spacing: 1,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: \"Auto-Role Assignment\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Automatically assign roles to new members when they join\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 701,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                            isChecked: settings.welcome.autoRole.enabled,\n                                                                            onChange: (e)=>handleWelcomeAutoRoleChange('enabled', e.target.checked),\n                                                                            isDisabled: !settings.welcome.enabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                            isDisabled: !settings.welcome.enabled || !settings.welcome.autoRole.enabled,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                    children: \"Roles to Assign\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    maxH: \"200px\",\n                                                                                    overflowY: \"auto\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CheckboxGroup, {\n                                                                                        value: settings.welcome.autoRole.roleIds,\n                                                                                        onChange: (values)=>handleWelcomeAutoRoleChange('roleIds', values),\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {\n                                                                                            columns: {\n                                                                                                base: 1,\n                                                                                                md: 2\n                                                                                            },\n                                                                                            spacing: 2,\n                                                                                            children: manageableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Checkbox, {\n                                                                                                    value: role.id,\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                                                w: 3,\n                                                                                                                h: 3,\n                                                                                                                borderRadius: \"full\",\n                                                                                                                bg: `#${role.color.toString(16).padStart(6, '0')}`\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 725,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                                children: role.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 731,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 724,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                }, role.id, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 723,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 721,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 717,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                manageableRoles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mt: 1,\n                                                                                    children: \"No manageable roles found. Make sure the bot has permission to manage roles.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 739,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {\n                                                                            allowToggle: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionButton, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                                flex: \"1\",\n                                                                                                textAlign: \"left\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                    fontWeight: \"medium\",\n                                                                                                    children: \"Advanced Auto-Role Settings\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 749,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 748,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionIcon, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 751,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 747,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionPanel, {\n                                                                                        pb: 4,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                            spacing: 4,\n                                                                                            align: \"stretch\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                    isDisabled: !settings.welcome.enabled || !settings.welcome.autoRole.enabled,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                            children: \"Assignment Delay (milliseconds)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 756,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInput, {\n                                                                                                            value: settings.welcome.autoRole.delay,\n                                                                                                            onChange: (_, value)=>handleWelcomeAutoRoleChange('delay', value),\n                                                                                                            min: 0,\n                                                                                                            max: 60000,\n                                                                                                            step: 100,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputField, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 764,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputStepper, {\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 766,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, this),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 767,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, this)\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 765,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 757,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                            fontSize: \"xs\",\n                                                                                                            color: \"gray.500\",\n                                                                                                            mt: 1,\n                                                                                                            children: \"Delay before assigning roles (useful if other bots need to process first)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 770,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 755,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                    display: \"flex\",\n                                                                                                    alignItems: \"center\",\n                                                                                                    isDisabled: !settings.welcome.enabled || !settings.welcome.autoRole.enabled,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                            htmlFor: \"retry-enabled\",\n                                                                                                            mb: \"0\",\n                                                                                                            children: \"Enable Retry on Failure\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 776,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                                                            id: \"retry-enabled\",\n                                                                                                            isChecked: settings.welcome.autoRole.retry.enabled,\n                                                                                                            onChange: (e)=>handleWelcomeAutoRoleRetryChange('enabled', e.target.checked)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 779,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 775,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                settings.welcome.autoRole.retry.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                                    spacing: 4,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                            isDisabled: !settings.welcome.enabled || !settings.welcome.autoRole.enabled,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                                    children: \"Max Retry Attempts\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 789,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInput, {\n                                                                                                                    value: settings.welcome.autoRole.retry.maxAttempts,\n                                                                                                                    onChange: (_, value)=>handleWelcomeAutoRoleRetryChange('maxAttempts', value),\n                                                                                                                    min: 1,\n                                                                                                                    max: 10,\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputField, {}, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 796,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, this),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputStepper, {\n                                                                                                                            children: [\n                                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                                    lineNumber: 798,\n                                                                                                                                    columnNumber: 47\n                                                                                                                                }, this),\n                                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                                    lineNumber: 799,\n                                                                                                                                    columnNumber: 47\n                                                                                                                                }, this)\n                                                                                                                            ]\n                                                                                                                        }, void 0, true, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 797,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, this)\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 790,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 788,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                            isDisabled: !settings.welcome.enabled || !settings.welcome.autoRole.enabled,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                                    children: \"Retry Delay (ms)\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 805,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInput, {\n                                                                                                                    value: settings.welcome.autoRole.retry.delayBetweenAttempts,\n                                                                                                                    onChange: (_, value)=>handleWelcomeAutoRoleRetryChange('delayBetweenAttempts', value),\n                                                                                                                    min: 1000,\n                                                                                                                    max: 30000,\n                                                                                                                    step: 1000,\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputField, {}, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 813,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, this),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberInputStepper, {\n                                                                                                                            children: [\n                                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                                    lineNumber: 815,\n                                                                                                                                    columnNumber: 47\n                                                                                                                                }, this),\n                                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                                    lineNumber: 816,\n                                                                                                                                    columnNumber: 47\n                                                                                                                                }, this)\n                                                                                                                            ]\n                                                                                                                        }, void 0, true, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                            lineNumber: 814,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, this)\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                    lineNumber: 806,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 804,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 787,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 754,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 753,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        opacity: !settings.welcome.enabled ? 0.6 : 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            align: \"start\",\n                                                                            spacing: 1,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: \"Random GIF Images\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 835,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 836,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                            isChecked: settings.welcome.nekosGif?.enabled || false,\n                                                                            onChange: (e)=>handleWelcomeNekosChange('enabled', e.target.checked),\n                                                                            isDisabled: !settings.welcome.enabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                            isDisabled: !settings.welcome.enabled || !settings.welcome.nekosGif?.enabled,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                    children: \"GIF Type\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                                                                                    value: settings.welcome.nekosGif?.type || 'wave',\n                                                                                    onChange: (e)=>handleWelcomeNekosChange('type', e.target.value),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"wave\",\n                                                                                            children: \"\\uD83D\\uDC4B Wave (Greeting)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 855,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"hug\",\n                                                                                            children: \"\\uD83E\\uDD17 Hug (Welcoming)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 856,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"pat\",\n                                                                                            children: \"\\uD83D\\uDC4B Pat (Friendly)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 857,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"happy\",\n                                                                                            children: \"\\uD83D\\uDE0A Happy (Cheerful)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"dance\",\n                                                                                            children: \"\\uD83D\\uDC83 Dance (Celebration)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 859,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"thumbsup\",\n                                                                                            children: \"\\uD83D\\uDC4D Thumbs Up (Approval)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 860,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 851,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"xs\",\n                                                                                    color: \"gray.500\",\n                                                                                    mt: 1,\n                                                                                    children: \"Choose the type of GIF to show when users don't have a custom banner\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 862,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Alert, {\n                                                                            status: \"info\",\n                                                                            size: \"sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertIcon, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 868,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertDescription, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 870,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 869,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 867,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: \"Goodbye System\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 886,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                            size: \"lg\",\n                                                                            isChecked: settings.goodbye.enabled,\n                                                                            onChange: (e)=>handleGoodbyeChange('enabled', e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 887,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                        isDisabled: !settings.goodbye.enabled,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiHash\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 899,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                            children: \"Goodbye Channel\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 900,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 898,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 897,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                                                                                placeholder: \"Select a channel\",\n                                                                                value: settings.goodbye.channelId || '',\n                                                                                onChange: (e)=>handleGoodbyeChange('channelId', e.target.value),\n                                                                                children: textChannels.map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: channel.id,\n                                                                                        children: [\n                                                                                            \"#\",\n                                                                                            channel.name\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 909,\n                                                                                        columnNumber: 35\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            textChannels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.500\",\n                                                                                mt: 1,\n                                                                                children: \"No text channels found. Make sure the bot has permission to view channels.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 915,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        opacity: !settings.goodbye.enabled ? 0.6 : 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            align: \"start\",\n                                                                            spacing: 1,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: \"Goodbye Message Templates\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 929,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create multiple templates - one will be randomly selected for each departing member\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 930,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 928,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                                                    colorScheme: \"red\",\n                                                                                    variant: \"subtle\",\n                                                                                    children: [\n                                                                                        settings.goodbye.messages.length,\n                                                                                        \" template\",\n                                                                                        settings.goodbye.messages.length !== 1 ? 's' : ''\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 935,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 939,\n                                                                                        columnNumber: 43\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"red\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: addGoodbyeMessage,\n                                                                                    isDisabled: !settings.goodbye.enabled,\n                                                                                    children: \"Add Template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 938,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                            spacing: 2,\n                                                                            wrap: \"wrap\",\n                                                                            children: settings.goodbye.messages.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: selectedGoodbyeMessage === index ? \"solid\" : \"outline\",\n                                                                                    colorScheme: \"red\",\n                                                                                    onClick: ()=>setSelectedGoodbyeMessage(index),\n                                                                                    isDisabled: !settings.goodbye.enabled,\n                                                                                    children: [\n                                                                                        \"Template \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 955,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 953,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        settings.goodbye.messages[selectedGoodbyeMessage] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                            p: 4,\n                                                                            borderWidth: 1,\n                                                                            borderRadius: \"md\",\n                                                                            bg: \"gray.50\",\n                                                                            _dark: {\n                                                                                bg: \"gray.800\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                spacing: 4,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                fontWeight: \"bold\",\n                                                                                                children: [\n                                                                                                    \"Template \",\n                                                                                                    selectedGoodbyeMessage + 1\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 973,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                                                                                        label: \"Duplicate Template\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                            \"aria-label\": \"Duplicate template\",\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 978,\n                                                                                                                columnNumber: 49\n                                                                                                            }, void 0),\n                                                                                                            size: \"sm\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            onClick: ()=>duplicateGoodbyeMessage(selectedGoodbyeMessage),\n                                                                                                            isDisabled: !settings.goodbye.enabled\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 976,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 975,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                                                                                        label: \"Delete Template\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                            \"aria-label\": \"Delete template\",\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrash2, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                                lineNumber: 988,\n                                                                                                                columnNumber: 49\n                                                                                                            }, void 0),\n                                                                                                            size: \"sm\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            colorScheme: \"red\",\n                                                                                                            onClick: ()=>removeGoodbyeMessage(selectedGoodbyeMessage),\n                                                                                                            isDisabled: !settings.goodbye.enabled || settings.goodbye.messages.length <= 1\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                            lineNumber: 986,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 985,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 974,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 972,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                        isDisabled: !settings.goodbye.enabled,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                children: \"Embed Title\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1000,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                value: settings.goodbye.messages[selectedGoodbyeMessage]?.title || '',\n                                                                                                onChange: (e)=>updateGoodbyeMessage(selectedGoodbyeMessage, 'title', e.target.value),\n                                                                                                placeholder: \"e.g., \\uD83D\\uDC4B Goodbye {userName}\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1001,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 999,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                        isDisabled: !settings.goodbye.enabled,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                children: \"Embed Description\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1009,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Textarea, {\n                                                                                                value: settings.goodbye.messages[selectedGoodbyeMessage]?.description || '',\n                                                                                                onChange: (e)=>updateGoodbyeMessage(selectedGoodbyeMessage, 'description', e.target.value),\n                                                                                                placeholder: \"Enter your goodbye message description...\",\n                                                                                                rows: 6\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1010,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1008,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        spacing: 4,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                isDisabled: !settings.goodbye.enabled,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                        children: \"Embed Color\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1020,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                        type: \"color\",\n                                                                                                        value: settings.goodbye.messages[selectedGoodbyeMessage]?.color || '#FF0000',\n                                                                                                        onChange: (e)=>updateGoodbyeMessage(selectedGoodbyeMessage, 'color', e.target.value),\n                                                                                                        w: \"100px\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1021,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1019,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                                                isDisabled: !settings.goodbye.enabled,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                                        children: \"Embed Footer\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1029,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                                                                                        value: settings.goodbye.messages[selectedGoodbyeMessage]?.footer || '',\n                                                                                                        onChange: (e)=>updateGoodbyeMessage(selectedGoodbyeMessage, 'footer', e.target.value),\n                                                                                                        placeholder: \"e.g., Safe travels!\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1030,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1028,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1018,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                        opacity: !settings.goodbye.enabled ? 0.6 : 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            align: \"start\",\n                                                                            spacing: 1,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: \"Random GIF Images\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1049,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1050,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1048,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Switch, {\n                                                                            isChecked: settings.goodbye.nekosGif?.enabled || false,\n                                                                            onChange: (e)=>handleGoodbyeNekosChange('enabled', e.target.checked),\n                                                                            isDisabled: !settings.goodbye.enabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1054,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 1047,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1046,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                                                            isDisabled: !settings.goodbye.enabled || !settings.goodbye.nekosGif?.enabled,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                                                                    children: \"GIF Type\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1064,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                                                                                    value: settings.goodbye.nekosGif?.type || 'cry',\n                                                                                    onChange: (e)=>handleGoodbyeNekosChange('type', e.target.value),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"cry\",\n                                                                                            children: \"\\uD83D\\uDE22 Cry (Sad Goodbye)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1069,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"wave\",\n                                                                                            children: \"\\uD83D\\uDC4B Wave (Farewell)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1070,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"sad\",\n                                                                                            children: \"\\uD83D\\uDE14 Sad (Melancholy)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1071,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"sleep\",\n                                                                                            children: \"\\uD83D\\uDE34 Sleep (Peaceful)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1072,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"pat\",\n                                                                                            children: \"\\uD83D\\uDC4B Pat (Comforting)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1073,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"hug\",\n                                                                                            children: \"\\uD83E\\uDD17 Hug (Supportive)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                            lineNumber: 1074,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1065,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                    fontSize: \"xs\",\n                                                                                    color: \"gray.500\",\n                                                                                    mt: 1,\n                                                                                    children: \"Choose the type of GIF to show when users don't have a custom banner\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1076,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1063,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Alert, {\n                                                                            status: \"info\",\n                                                                            size: \"sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertIcon, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1082,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertDescription, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1084,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1083,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1081,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                    lineNumber: 1062,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1061,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Alert, {\n                                                        status: \"info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertIcon, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1099,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertTitle, {\n                                                                        children: \"Placeholder Guide\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AlertDescription, {\n                                                                        children: \"Use these placeholders in your message templates. They will be automatically replaced with actual values when messages are sent.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 1098,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            color: \"blue.500\",\n                                                                            children: \"\\uD83D\\uDC64 User Placeholders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1112,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1111,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: PLACEHOLDERS.user.map((placeholder, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    bg: \"blue.50\",\n                                                                                    _dark: {\n                                                                                        bg: \"blue.900\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                                align: \"start\",\n                                                                                                spacing: 1,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontFamily: \"mono\",\n                                                                                                        fontWeight: \"bold\",\n                                                                                                        color: \"blue.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"blue.300\"\n                                                                                                        },\n                                                                                                        children: placeholder.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1120,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontSize: \"sm\",\n                                                                                                        color: \"gray.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"gray.400\"\n                                                                                                        },\n                                                                                                        children: placeholder.description\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1123,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1119,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                \"aria-label\": \"Copy placeholder\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 1129,\n                                                                                                    columnNumber: 45\n                                                                                                }, void 0),\n                                                                                                size: \"sm\",\n                                                                                                variant: \"ghost\",\n                                                                                                onClick: ()=>{\n                                                                                                    navigator.clipboard.writeText(placeholder.name);\n                                                                                                    toast({\n                                                                                                        title: 'Copied!',\n                                                                                                        description: `${placeholder.name} copied to clipboard`,\n                                                                                                        status: 'success',\n                                                                                                        duration: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1127,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1118,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1117,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1115,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            color: \"green.500\",\n                                                                            children: \"\\uD83C\\uDFF0 Server Placeholders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1152,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: PLACEHOLDERS.guild.map((placeholder, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    bg: \"green.50\",\n                                                                                    _dark: {\n                                                                                        bg: \"green.900\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                                align: \"start\",\n                                                                                                spacing: 1,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontFamily: \"mono\",\n                                                                                                        fontWeight: \"bold\",\n                                                                                                        color: \"green.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"green.300\"\n                                                                                                        },\n                                                                                                        children: placeholder.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1160,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontSize: \"sm\",\n                                                                                                        color: \"gray.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"gray.400\"\n                                                                                                        },\n                                                                                                        children: placeholder.description\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1163,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1159,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                \"aria-label\": \"Copy placeholder\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 1169,\n                                                                                                    columnNumber: 45\n                                                                                                }, void 0),\n                                                                                                size: \"sm\",\n                                                                                                variant: \"ghost\",\n                                                                                                onClick: ()=>{\n                                                                                                    navigator.clipboard.writeText(placeholder.name);\n                                                                                                    toast({\n                                                                                                        title: 'Copied!',\n                                                                                                        description: `${placeholder.name} copied to clipboard`,\n                                                                                                        status: 'success',\n                                                                                                        duration: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1167,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1158,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1157,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1155,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1150,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            color: \"purple.500\",\n                                                                            children: \"⏰ Time Placeholders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1192,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1191,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: PLACEHOLDERS.time.map((placeholder, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    bg: \"purple.50\",\n                                                                                    _dark: {\n                                                                                        bg: \"purple.900\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                                align: \"start\",\n                                                                                                spacing: 1,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontFamily: \"mono\",\n                                                                                                        fontWeight: \"bold\",\n                                                                                                        color: \"purple.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"purple.300\"\n                                                                                                        },\n                                                                                                        children: placeholder.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1200,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontSize: \"sm\",\n                                                                                                        color: \"gray.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"gray.400\"\n                                                                                                        },\n                                                                                                        children: placeholder.description\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1203,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1199,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                \"aria-label\": \"Copy placeholder\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 1209,\n                                                                                                    columnNumber: 45\n                                                                                                }, void 0),\n                                                                                                size: \"sm\",\n                                                                                                variant: \"ghost\",\n                                                                                                onClick: ()=>{\n                                                                                                    navigator.clipboard.writeText(placeholder.name);\n                                                                                                    toast({\n                                                                                                        title: 'Copied!',\n                                                                                                        description: `${placeholder.name} copied to clipboard`,\n                                                                                                        status: 'success',\n                                                                                                        duration: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1207,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1198,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1197,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1195,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1194,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            color: \"orange.500\",\n                                                                            children: \"\\uD83C\\uDF89 Welcome Only\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1232,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1231,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: PLACEHOLDERS.welcome.map((placeholder, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    bg: \"orange.50\",\n                                                                                    _dark: {\n                                                                                        bg: \"orange.900\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                                align: \"start\",\n                                                                                                spacing: 1,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontFamily: \"mono\",\n                                                                                                        fontWeight: \"bold\",\n                                                                                                        color: \"orange.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"orange.300\"\n                                                                                                        },\n                                                                                                        children: placeholder.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1240,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontSize: \"sm\",\n                                                                                                        color: \"gray.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"gray.400\"\n                                                                                                        },\n                                                                                                        children: placeholder.description\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1243,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1239,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                \"aria-label\": \"Copy placeholder\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 1249,\n                                                                                                    columnNumber: 45\n                                                                                                }, void 0),\n                                                                                                size: \"sm\",\n                                                                                                variant: \"ghost\",\n                                                                                                onClick: ()=>{\n                                                                                                    navigator.clipboard.writeText(placeholder.name);\n                                                                                                    toast({\n                                                                                                        title: 'Copied!',\n                                                                                                        description: `${placeholder.name} copied to clipboard`,\n                                                                                                        status: 'success',\n                                                                                                        duration: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1247,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1238,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1235,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1230,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                                                            size: \"md\",\n                                                                            color: \"red.500\",\n                                                                            children: \"\\uD83D\\uDC4B Goodbye Only\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1272,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1271,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: PLACEHOLDERS.goodbye.map((placeholder, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                                                                    p: 3,\n                                                                                    borderWidth: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    bg: \"red.50\",\n                                                                                    _dark: {\n                                                                                        bg: \"red.900\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                                                        justify: \"space-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                                                                                align: \"start\",\n                                                                                                spacing: 1,\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontFamily: \"mono\",\n                                                                                                        fontWeight: \"bold\",\n                                                                                                        color: \"red.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"red.300\"\n                                                                                                        },\n                                                                                                        children: placeholder.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1280,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                                                                        fontSize: \"sm\",\n                                                                                                        color: \"gray.600\",\n                                                                                                        _dark: {\n                                                                                                            color: \"gray.400\"\n                                                                                                        },\n                                                                                                        children: placeholder.description\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                        lineNumber: 1283,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1279,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                                                                                \"aria-label\": \"Copy placeholder\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                    lineNumber: 1289,\n                                                                                                    columnNumber: 45\n                                                                                                }, void 0),\n                                                                                                size: \"sm\",\n                                                                                                variant: \"ghost\",\n                                                                                                onClick: ()=>{\n                                                                                                    navigator.clipboard.writeText(placeholder.name);\n                                                                                                    toast({\n                                                                                                        title: 'Copied!',\n                                                                                                        description: `${placeholder.name} copied to clipboard`,\n                                                                                                        status: 'success',\n                                                                                                        duration: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                                lineNumber: 1287,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                        lineNumber: 1278,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                                    lineNumber: 1277,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                            lineNumber: 1275,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                                lineNumber: 1270,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                                lineNumber: 1097,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                            lineNumber: 1096,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalFooter, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 1317,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    colorScheme: \"blue\",\n                                    onClick: handleSave,\n                                    isLoading: isSaving,\n                                    isDisabled: isLoading,\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiHash_FiLogOut_FiMessageSquare_FiPlus_FiSettings_FiShuffle_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiShuffle, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                        lineNumber: 1325,\n                                        columnNumber: 27\n                                    }, void 0),\n                                    children: \"Save Welcome & Goodbye System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                                    lineNumber: 1320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                            lineNumber: 1316,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                        lineNumber: 1315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n                lineNumber: 509,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\WelcomeSystemDialog.tsx\",\n        lineNumber: 507,\n        columnNumber: 7\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/WelcomeSystemDialog.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiCopy,FiHash,FiLogOut,FiMessageSquare,FiPlus,FiSettings,FiShuffle,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiCopy,FiHash,FiLogOut,FiMessageSquare,FiPlus,FiSettings,FiShuffle,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQ29weSxGaUhhc2gsRmlMb2dPdXQsRmlNZXNzYWdlU3F1YXJlLEZpUGx1cyxGaVNldHRpbmdzLEZpU2h1ZmZsZSxGaVRyYXNoMiE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiCopy,FiHash,FiLogOut,FiMessageSquare,FiPlus,FiSettings,FiShuffle,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ })

};
;